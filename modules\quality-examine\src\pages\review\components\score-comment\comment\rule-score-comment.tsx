import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import './index.less';
import { isEmptyValues } from '@uni/utils/src/utils';
import { GridStack } from '@uni/grid/src/core/gridstack';
import { Emitter } from '@uni/utils/src/emitter';
import { cloneDeep, orderBy, uniq } from 'lodash';
import '@uni/grid/src/core/gridstack.css';
import '@uni/grid/src/style/index';
import {
  Button,
  Dropdown,
  Empty,
  Form,
  Input,
  MenuProps,
  message,
  Modal,
  Spin,
  Tooltip,
} from 'antd';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import {
  CheckCircleOutlined,
  CloseOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  IssuesCloseOutlined,
  MenuUnfoldOutlined,
  QuestionCircleOutlined,
  SendOutlined,
  StopOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import {
  CommentCategoryItem,
  CommentDetailItem,
  RuleItem,
} from '@/pages/review/components/score-comment/score/interfaces';
import {
  addDmrHasCommentStyle,
  canCommentAdd,
  containerKeyProcessor,
  deleteDmrHasCommentStyle,
  isSpecialColumnNameKey,
  tableItemFeeItemSpecialKeyProcessor,
  removeAllCommentStyle,
  ruleDetailCodeFinder,
  categoryCodeFinder,
  canCommentAddInRuleScore,
} from '@/pages/review/components/score-comment/comment/utils';
import comment from '@/pages/review/components/score-comment/comment/index';
import { useUpdate, useUpdateEffect } from 'ahooks';
import {
  BatchItem,
  BatchMasterSysItem,
  IssueItem,
} from '@/pages/review/interface';
import AppealModal from '@/pages/review/components/score-comment/comment/appeal';
import { useRequest } from 'umi';
import { ScoreRestrictNumberInput } from '@/pages/review/components/score-comment/score/input';
import { numberInputRestrictKeyDown } from '@uni/grid/src/utils';
import { decimalMap } from '@/pages/review/components/score-comment/score/constant';
import { QualityExamineDetailStatus } from '@/pages/review/components/score-comment/comment/constants';
import { useModel } from '@@/plugin-model/useModel';
import { UniSelect } from '@uni/components/src';
import AnnotationAdd from '@/pages/review/components/score-comment/comment/annotation-add';

const { TextArea } = Input;

const reviewerCommentOperationItems: MenuProps['items'] = [
  {
    key: 'DELETE',
    label: '删除',
    icon: <DeleteOutlined />,
  },
  {
    key: 'RESOLVE',
    label: '确认修复',
    icon: <CheckCircleOutlined style={{ color: '#4ebb4a' }} />,
  },
  {
    key: 'APPEAL_VALID',
    label: '接受申诉',
    icon: <IssuesCloseOutlined style={{ color: '#4ebb4a' }} />,
  },
  {
    key: 'APPEAL_INVALID',
    label: '驳回申诉',
    icon: <StopOutlined style={{ color: '#0466c8' }} />,
  },
];

const revieweeCommentOperationItems: MenuProps['items'] = [
  {
    key: 'REVIEWEE_RESOLVE',
    label: '整改完成',
    icon: <CheckCircleOutlined style={{ color: '#4ebb4a' }} />,
  },
  {
    key: 'APPEAL',
    label: '申诉',
    icon: <QuestionCircleOutlined style={{ color: '#f8961e' }} />,
  },
];

export interface RuleScoreCommentProps {
  sysMasterItem?: BatchMasterSysItem;

  taskItemRef: any;

  dmrGridContainerRef: any;
  detailCommentRef?: any;
  svgLinesContainerRef: any;
  taskId?: string;
  taskStatus?: string;

  commentRuleDetails?: RuleItem[];
  commentDetails?: CommentDetailItem[];

  onCommentsUpdate?: (comments: any[]) => void;

  onDmrContainerReady?: () => void;

  tableReadonly?: boolean;

  onIssueUpdate?: () => void;
}

interface RuleScoreCommentItem {
  formKey: string;
  containerKey?: string;
  label?: string;
  x?: number;
  y?: number;
  type?: 'ADD' | 'EDIT';
  date?: string;
  uniqueId?: string;

  // detailItem
  detailId?: number;
  editMode?: boolean;
  remark?: string;
  inputScore?: string | number;

  issueId?: number | string;
  issueItem?: IssueItem;

  originDetailItem?: any;

  categoryItem?: CommentCategoryItem;
  RuleCode?: string;
}

export const ruleScoreItemKeys = [
  {
    key: 'OriginalInputValue',
    label: '原始值',
  },
  {
    key: 'DisplayErrMsg',
    label: '规则名称',
  },
  {
    key: 'InputScore',
    label: '减分',
  },
  {
    key: 'Remark',
    label: '评审意见',
  },
  {
    key: 'Creator',
    label: '创建者',
  },
];

const RuleScoreComment = (props: RuleScoreCommentProps) => {
  const { globalState } = useModel('@@qiankunStateFromMaster');

  const [form] = Form.useForm();

  const annotationAddContainerRef = useRef(null);

  const userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');

  const commentContainerScrollTop = useRef(0);

  const dmrContainerScrollTop = useRef(0);

  const [commentLoading, setCommentLoading] = useState(true);

  const appealModalRef = useRef(null);

  const dmrContainerRect = document
    ?.getElementById('dmr-content-container')
    ?.getBoundingClientRect();

  const commentContainerRect = document
    ?.getElementById('rule-score-comment-form')
    ?.getBoundingClientRect();

  React.useImperativeHandle(props?.detailCommentRef, () => {
    return {
      getComments: () => {
        return comments;
      },
      scrollAlongWithDmr: (closestElement) => {
        // document
        //   ?.querySelector(`div[id^='RULE-SCORE-ITEM-${closestElement?.id}'`)
        //   ?.scrollIntoView({
        //     behavior: 'smooth',
        //     block: 'start',
        //     inline: 'nearest',
        //   });
      },
      addComment: (data: any) => {
        // if (!canCommentAddInRuleScore(data, props?.commentRuleDetails)) {
        //   // TODO 这里变成要 对于layout来说的 category类型了？？？？ 这要怎么配置哦....
        //   message.error(`“${data?.label}”未配置规则，无法创建`);
        //   return;
        // }
        console.log(
          '批注预备用Console Inside.addComment',
          data,
          props?.commentRuleDetails,
          ruleDetailCodeFinder(data?.formKey, props?.commentRuleDetails),
          props?.commentRuleDetails?.find(
            (ruleItem) => ruleItem?.RuleCode === ruleCode,
          ),
        );
        const ruleCode = ruleDetailCodeFinder(
          data?.formKey,
          props?.commentRuleDetails,
        );
        let ruleItem = props?.commentRuleDetails?.find(
          (ruleItem) => ruleItem?.RuleCode === ruleCode,
        );

        Modal.confirm({
          title: `添加批注`,
          icon: null,
          zIndex: 10050,
          className: 'annotation-add-container',
          content: (
            <AnnotationAdd
              containerRef={annotationAddContainerRef}
              taskId={props?.taskId}
              tableReadonly={props?.tableReadonly}
              ruleItem={ruleItem}
              commentInitialData={{
                ...data,
                RuleCode: ruleCode,
                ruleItem: ruleItem,
              }}
              commentItem={{
                ...data,
                ruleItem: ruleItem,
              }}
              commentRuleDetails={props?.commentRuleDetails}
              taskStatus={props?.taskStatus}
            />
          ),
          onOk: async () => {
            let values =
              annotationAddContainerRef?.current?.getAnnotationContent();
            // TODO 调用接口 新建批注
            console.log('Values', values);

            if (isEmptyValues(values?.RuleCode)) {
              message.open({
                content: '请选择规则',
                type: 'error',
                getPopupContainer: () =>
                  document.getElementById('annotation-add-container'),
              });
              return Promise.reject();
            }

            if (isEmptyValues(values?.Remark)) {
              message.open({
                content: '请输入意见',
                type: 'error',
                getPopupContainer: () =>
                  document.getElementById('annotation-add-container'),
              });
              return Promise.reject();
            }

            if (
              ruleItem?.AllowInputScore === true &&
              isEmptyValues(values?.InputScore)
            ) {
              message.open({
                content: '请输入分数',
                type: 'error',
                getPopupContainer: () =>
                  document.getElementById('annotation-add-container'),
              });
              return Promise.reject();
            }

            const columnName = tableItemFeeItemSpecialKeyProcessor(
              data?.formKey,
            );

            let requestData = {
              taskId: props?.taskId,
              ruleCode: values?.RuleCode,
              remark: values?.Remark,
              inputScore: values?.InputScore,
              ColumnName: columnName,
              OriginalInputValue: data?.OriginalInputValue,
              CreatorId: userInfo?.UserId,
            };

            let upsertCategoryDetailResponse: RespVO<CommentDetailItem> =
              await uniCommonService(
                'Api/Dmr/DmrCardQualityExamine/UpsertRuleDetail',
                {
                  method: 'POST',
                  data: requestData,
                },
              );

            if (
              upsertCategoryDetailResponse?.code === 0 &&
              upsertCategoryDetailResponse?.statusCode === 200
            ) {
              // TODO 插入 detail

              commentItemModalOkUpdate({
                ...data,
                Id: upsertCategoryDetailResponse?.data,
                Remark: values?.Remark,
                InputScore: values?.InputScore,
                ColumnName: columnName,
                CreationTime: dayjs().format('YYYY-MM-DD HH:mm'),
                RuleCode: values?.RuleCode,
                OriginalInputValue: data?.OriginalInputValue,
                Creator: userInfo,
                CreatorName: userInfo?.Name,
                CreatorId: userInfo?.UserId,
              });
            } else {
              message.error('添加批注出现错误，请联系管理员');
            }

            // commentItemInitialize(data, true);
          },
          onCancel: () => {},
        });
      },
    };
  });

  const [comments, setComments] = useState<RuleScoreCommentItem[]>([]);

  // 首次进来的时候 初始化现有的 comments
  const commentsDetailItemInitialize = async () => {
    removeAllCommentStyle();
    form?.resetFields();
    setCommentLoading(true);
    if (!isEmptyValues(props?.commentDetails)) {
      let comments: RuleScoreCommentItem[] = [];

      let instances =
        props?.dmrGridContainerRef?.current?.getGridStackInstance();
      let contentItems = [];
      if (!isEmptyValues(instances)) {
        contentItems = instances?.contentGridInstance?.getGridItems();
      }

      let issueDetails = [];
      let issueIds = props?.commentDetails
        ?.filter((item) => {
          return !isEmptyValues(item?.IssueId);
        })
        ?.map((item) => {
          return item?.IssueId;
        });
      if (!isEmptyValues(issueIds)) {
        let currentDetailIssuesResponse: RespVO<IssueItem[]> =
          await getDetailIssuesReq(issueIds);
        if (
          currentDetailIssuesResponse?.code === 0 &&
          currentDetailIssuesResponse?.statusCode === 200
        ) {
          issueDetails = currentDetailIssuesResponse?.data;
        }
      }

      for (let detailItem of props?.commentDetails) {
        let containerKey = detailItem?.ColumnName;
        let commentItem: RuleScoreCommentItem = {
          formKey: detailItem?.ColumnName,
          containerKey: containerKey,
          detailId: detailItem?.Id,
          issueId: detailItem?.IssueId,
          remark: detailItem?.Remark,
          date: dayjs(detailItem?.CreationTime).format('YYYY-MM-DD HH:mm'),
        };

        commentItem['uniqueId'] = detailItem?.Id?.toString();
        commentItem['type'] = 'EDIT';
        commentItem['editMode'] = false;
        // commentItem['label'] = currentMainDmrItem?.data?.prefix;
        commentItem['originDetailItem'] = cloneDeep(detailItem);

        if (!isEmptyValues(detailItem?.IssueId)) {
          let issueItem = issueDetails?.find((item) => {
            return item.IssueId === detailItem?.IssueId;
          });
          if (!isEmptyValues(issueItem)) {
            for (let issueEventItem of issueItem?.IssueEvents) {
              let userInfoResponse = await getDetailIssueEventUserInfo(
                issueItem?.EventBy,
              );
              issueEventItem['eventUserInfo'] = userInfoResponse?.data;
            }

            commentItem['issueItem'] = issueItem;
          }
        }

        // 真实remark
        form.setFieldValue(
          `comment-${commentItem?.formKey}-${commentItem?.uniqueId}`,
          detailItem?.Remark ?? '',
        );

        form.setFieldValue(
          `score-${commentItem?.formKey}-${commentItem?.uniqueId}`,
          detailItem?.InputScore ?? '',
        );

        comments.push(commentItem);
      }

      console.log('Comments', comments, props?.commentDetails);

      commentItemOtherPropsProcessor(comments);

      setComments(comments);
    } else {
      setComments([]);
    }

    setCommentLoading(false);
  };

  useUpdateEffect(() => {
    commentsDetailItemInitialize();
  }, [props?.commentDetails]);

  useEffect(() => {
    props?.onCommentsUpdate && props?.onCommentsUpdate(comments);
  }, [comments]);

  useUpdateEffect(() => {
    if (commentLoading === false) {
      props?.svgLinesContainerRef?.current?.setPositions(
        comments?.map((commentItem) => {
          return {
            start: tableItemFeeItemSpecialKeyProcessor(commentItem?.formKey),
            end: `RULE-SCORE-ITEM-${commentItem?.formKey}-${commentItem?.uniqueId}`,
          };
        }),
      );
    }
  }, [commentLoading, comments]);

  // useLayoutEffect(() => {
  //   (global?.window as any)?.eventEmitter?.on(
  //     'CONTENT_SCROLL_FOR_COMMENT',
  //     () => {
  //       const commentScrollTop = document.getElementById(
  //         'rule-score-comment-form',
  //       ).scrollTop;
  //       const dmrScrollTop = document.getElementById(
  //         'dmr-content-container',
  //       ).scrollTop;
  //
  //       let lastInvisibleCommentIndexInComment = null;
  //       let firstVisibleCommentIndexInComment = null;
  //
  //       // 要判定是怎么滚动 上还是下....
  //       let scrollUp = dmrScrollTop > dmrContainerScrollTop?.current;
  //       dmrContainerScrollTop.current = dmrScrollTop;
  //
  //       const orderedComments = (
  //         scrollUp
  //           ? orderBy(comments, ['position', 'y', 'x'], ['asc', 'desc', 'asc'])
  //           : orderBy(comments, ['position', 'y', 'x'], ['desc', 'asc', 'desc'])
  //       ).filter((item) => {
  //         return item?.position !== 'HEADER';
  //       });
  //
  //       let scrollDistance = dmrScrollTop;
  //
  //       for (let index = 0; index < orderedComments.length; index++) {
  //         const commentItem = orderedComments?.at(index);
  //         const currentCommentItemRect = document
  //           ?.querySelector(`div[id^='RULE-SCORE-ITEM-${commentItem?.formKey}'`)
  //           ?.getBoundingClientRect();
  //
  //         commentItem['top'] = currentCommentItemRect?.top;
  //         commentItem['bottom'] = currentCommentItemRect?.bottom;
  //
  //         if (commentItem?.formKey) {
  //           let dmrElement = document.querySelector(
  //             `#dmr-form-container #dmr-content-container #${
  //               commentItem?.containerKey ?? commentItem?.formKey
  //             }`,
  //           );
  //           let commentElement = document?.querySelector(
  //             `div[id^='RULE-SCORE-ITEM-${commentItem?.formKey}'`,
  //           ) as any;
  //
  //           if (dmrElement !== null && commentElement !== null) {
  //             let dmrElementInViewport =
  //               dmrElement?.getBoundingClientRect()?.top >=
  //               dmrContainerRect?.top;
  //             let commentElementInViewport =
  //               commentElement?.getBoundingClientRect()?.top >=
  //               commentContainerRect?.top;
  //
  //             // 表示 comment 应该滚上去
  //             if (
  //               dmrElementInViewport === false &&
  //               commentElementInViewport === true
  //             ) {
  //               scrollDistance = Math.max(
  //                 scrollDistance,
  //                 commentElement?.offsetTop + commentElement?.offsetHeight,
  //               );
  //             }
  //
  //             // 表示 comment 应该滚下来
  //             if (
  //               dmrElementInViewport === true &&
  //               commentElementInViewport === false
  //             ) {
  //               scrollDistance = Math.min(
  //                 scrollDistance,
  //                 commentElement?.offsetTop - 100,
  //               );
  //             }
  //           }
  //         }
  //       }
  //
  //       if (scrollUp) {
  //         // 滚轮往下拉
  //         if (commentScrollTop >= scrollDistance) {
  //           return;
  //         }
  //         // 判定是不是应该显示
  //       } else {
  //         // 滚轮往上推
  //         if (commentScrollTop < scrollDistance) {
  //           return;
  //         }
  //
  //         /*// 判定是不是应该显示
  //       // 拿到第一个bottom 比滚动区大的那一个 如果说 这个不应该显示 那就滚到他这个bottom上
  //       let firstGreaterThanDistanceItem = null;
  //       let firstGreaterThanDistanceCommentItem = null;
  //       for (let orderedCommentItem of orderedComments) {
  //           let orderCommentDomItem = document.getElementById(`RULE-SCORE-ITEM-${orderedCommentItem?.formKey}`);
  //           if((orderCommentDomItem?.offsetTop + orderCommentDomItem?.offsetHeight) > scrollDistance) {
  //             firstGreaterThanDistanceItem = orderCommentDomItem;
  //             firstGreaterThanDistanceCommentItem = orderedCommentItem;
  //             break;
  //           }
  //       }
  //       if(firstGreaterThanDistanceItem) {
  //         let dmrElement = document.querySelector(
  //           `#dmr-form-container #${firstGreaterThanDistanceCommentItem?.containerKey ?? firstGreaterThanDistanceCommentItem?.formKey}`,
  //         );
  //         if(dmrElement) {
  //           let dmrElementInViewport =
  //             dmrElement?.getBoundingClientRect()?.top >= dmrContainerRect?.top;
  //           if(dmrElementInViewport === false) {
  //             scrollDistance = firstGreaterThanDistanceItem?.offsetTop + firstGreaterThanDistanceItem?.offsetHeight;
  //           }
  //         }
  //       }*/
  //       }
  //
  //       setTimeout(() => {
  //         document.getElementById('rule-score-comment-form').scrollTo({
  //           top: scrollDistance,
  //           behavior: 'smooth',
  //         });
  //       }, 0);
  //     },
  //   );
  //
  //   return () => {
  //     (global?.window as any)?.eventEmitter?.off('CONTENT_SCROLL_FOR_COMMENT');
  //   };
  // }, [comments]);

  const commentItemModalOkUpdate = (detailItem: any) => {
    let currentComments = cloneDeep(comments);

    let containerKey = detailItem?.ColumnName;
    let commentItem: RuleScoreCommentItem = {
      formKey: detailItem?.ColumnName,
      containerKey: containerKey,
      detailId: detailItem?.Id,
      issueId: detailItem?.IssueId,
      remark: detailItem?.Remark,
      inputScore: detailItem?.InputScore,
      date: dayjs(detailItem?.CreationTime).format('YYYY-MM-DD HH:mm'),
      RuleCode: detailItem?.RuleCode,
    };

    commentItem['uniqueId'] = detailItem?.Id?.toString();
    commentItem['type'] = 'EDIT';
    commentItem['editMode'] = false;
    // commentItem['label'] = currentMainDmrItem?.data?.prefix;
    commentItem['originDetailItem'] = cloneDeep(detailItem);

    // 真实remark
    form.setFieldValue(
      `comment-${commentItem?.formKey}-${commentItem?.uniqueId}`,
      detailItem?.Remark ?? '',
    );

    form.setFieldValue(
      `score-${commentItem?.formKey}-${commentItem?.uniqueId}`,
      detailItem?.InputScore ?? '',
    );

    currentComments.push(commentItem);

    commentItemOtherPropsProcessor(currentComments);

    let orderedComments = orderBy(
      currentComments,
      ['position', 'y', 'x'],
      ['desc', 'asc', 'desc'],
    );
    setComments(orderedComments);

    // 添加 样式
    addDmrHasCommentStyle(commentItem);

    props?.svgLinesContainerRef?.current?.updatePolyLine();
  };

  const commentItemInitialize = (commentItem: any, existLocate = false) => {
    let currentComments = cloneDeep(comments);

    let formKeyToCommentItem = currentComments?.find(
      (item) => item?.formKey === commentItem?.formKey,
    );

    //更新一下comments 的xy
    commentItem['uniqueId'] = Math.round(Date.now() / 1000);
    commentItem['type'] = 'ADD';
    commentItem['editMode'] = true;
    commentItem['date'] = dayjs().format('YYYY-MM-DD HH:mm');

    currentComments.push(commentItem);

    commentItemOtherPropsProcessor(currentComments);

    let orderedComments = orderBy(
      currentComments,
      ['position', 'y', 'x'],
      ['desc', 'asc', 'desc'],
    );
    setComments(orderedComments);

    // 添加 样式
    addDmrHasCommentStyle(commentItem);
  };

  const commentItemOtherPropsProcessor = (comments: any[]) => {
    let instances = props?.dmrGridContainerRef?.current?.getGridStackInstance();

    let contentItems = [];
    let headerItems = [];
    if (!isEmptyValues(instances)) {
      contentItems = instances?.contentGridInstance?.getGridItems();
      headerItems = instances?.headerGridInstance?.getGridItems();
    }

    comments?.forEach((commentItem) => {
      // containerKey 换算
      if (isSpecialColumnNameKey(commentItem?.formKey)) {
        // 特殊处理一下
        commentItem['containerKey'] = containerKeyProcessor(
          commentItem?.formKey,
        )?.containerKey;
      }

      let currentMainDmrItem = contentItems?.find(
        (contentItem) =>
          contentItem?.gridstackNode?.id ===
          (commentItem?.containerKey ?? commentItem?.formKey),
      );

      let currentDmrHeaderItem = headerItems?.find(
        (contentItem) =>
          contentItem?.gridstackNode?.id ===
          (commentItem?.containerKey ?? commentItem?.formKey),
      );

      if (currentMainDmrItem) {
        commentItem['position'] = 'CONTENT';
        commentItem['x'] = currentMainDmrItem?.gridstackNode?.x ?? 0;
        commentItem['y'] = currentMainDmrItem?.gridstackNode?.y ?? 0;
      }

      if (currentDmrHeaderItem) {
        commentItem['position'] = 'HEADER';
        commentItem['x'] = currentDmrHeaderItem?.gridstackNode?.x ?? 0;
        commentItem['y'] = currentDmrHeaderItem?.gridstackNode?.y ?? 0;
      }

      //给个default值
      if (isEmptyValues(commentItem['x'])) {
        commentItem['x'] = 65535;
      }

      if (isEmptyValues(commentItem['y'])) {
        commentItem['y'] = 65535;
      }

      // 添加 样式
      addDmrHasCommentStyle(commentItem);

      const ruleCode =
        commentItem?.['originDetailItem']?.RuleCode ??
        ruleDetailCodeFinder(commentItem?.formKey, props?.commentRuleDetails);
      commentItem['ruleItem'] = ruleCode
        ? props?.commentRuleDetails?.find(
            (ruleItem) => ruleItem?.RuleCode === ruleCode,
          )
        : null;
    });
  };

  useEffect(() => {
    (global?.window as any)?.eventEmitter?.on(
      'DMR_COMMENT_NOTIFY_STYLE',
      () => {
        let newComments = comments?.slice();
        commentItemOtherPropsProcessor(newComments);
        setComments(newComments);

        props?.onDmrContainerReady && props?.onDmrContainerReady();
      },
    );

    return () => {
      (global?.window as any)?.eventEmitter?.off('DMR_COMMENT_NOTIFY_STYLE');
    };
  }, [comments]);

  const onCommentItemDelete = (formKey: string, uniqueId: string) => {
    let waitRemoveElement = document?.getElementById(
      `RULE-SCORE-ITEM-${formKey}-${uniqueId}`,
    );
    if (waitRemoveElement === null || waitRemoveElement === undefined) {
      message.error('删除的项不存在');
      return;
    }

    // 删除comment
    let currentComments = cloneDeep(comments);
    let index = currentComments?.findIndex((item) => item?.formKey === formKey);
    if (index !== -1) {
      currentComments.splice(index, 1);
    }
    setComments(currentComments);
    // 删除Connector线
    setTimeout(() => {
      props?.svgLinesContainerRef?.current?.setPositions(
        currentComments?.map((commentItem) => {
          return {
            start: tableItemFeeItemSpecialKeyProcessor(commentItem?.formKey),
            end: `RULE-SCORE-ITEM-${commentItem?.formKey}-${uniqueId}`,
          };
        }),
      );
    }, 200);
    // 删除对应的项的 高亮
    deleteDmrHasCommentStyle(formKey);
  };

  const onCommentItemUpdate = (
    formKey: string,
    detailId: number,
    type: string,
    updateKey: string,
  ) => {
    let value = form?.getFieldValue(`${type}-${formKey}-${detailId}`);
    let data = {
      editMode: false,
      detailId: detailId,
      type: 'EDIT',
      uniqueId: detailId,
    };
    data[updateKey] = value;
    updateCommentItemPropsViaFormKey(formKey, data);

    props?.svgLinesContainerRef?.current?.updatePolyLine();
  };

  const updateCommentItemPropsViaFormKey = (formKey: string, props: any) => {
    let newComments = cloneDeep(comments);
    let currentComment = newComments?.find((item) => item?.formKey === formKey);
    if (currentComment !== null && currentComment !== undefined) {
      Object.keys(props)?.forEach((key) => {
        currentComment[key] = props?.[key];

        if (key === 'editMode') {
          if (props?.[key] === true) {
            props?.svgLinesContainerRef?.current?.setActiveEndId(
              `RULE-SCORE-ITEM-${formKey}-${props?.uniqueId}`,
            );
          } else {
            props?.svgLinesContainerRef?.current?.setActiveEndId(undefined);
          }
        }

        if (key === 'Status') {
          // 更新 originDetailItem
          currentComment['originDetailItem'][key] = props?.[key];
        }
      });

      setComments(newComments);
    }

    return newComments;
  };

  const onContextMenuItemClick = async (info: any, commentItem: any) => {
    switch (info?.key) {
      case 'EDIT':
        // 编辑批注
        updateCommentItemPropsViaFormKey(commentItem?.formKey, {
          editMode: true,
        });
        break;
      case 'DELETE':
        if (commentItem?.type === 'EDIT') {
          // 删除就行
          // 不止删除 还要 调接口删除
          let data = {
            taskId: props?.taskId,
            detailId: commentItem?.detailId,
          };
          await uniCommonService('Api/Dmr/DmrCardQualityExamine/DeleteDetail', {
            method: 'POST',
            data: data,
          });
        }

        onCommentItemDelete(commentItem?.formKey, commentItem?.uniqueId);
        break;
      case 'RESOLVE': {
        let data = {
          detailId: commentItem?.detailId,
        };
        let resolveResponse = await uniCommonService(
          'Api/Dmr/DmrCardQualityExamine/ResolveDetailByReviewer',
          {
            method: 'POST',
            data: data,
          },
        );

        if (resolveResponse?.statusCode === 200) {
          // 更新当前detail status
          updateCommentItemPropsViaFormKey(commentItem?.formKey, {
            Status: 'Resolved',
          });

          props?.taskItemRef?.current?.updateTaskItem(props?.taskId);
        }
        break;
      }
      case 'APPEAL_VALID':
        appealModalRef?.current?.showAppeal({
          status: true,
          title: '填写接受理由',
          detailId: commentItem?.detailId,
          type: info.key,
          onSuccess: async (data: any) => {
            // TODO 刷新 当前detail Id 下面的 issues
            // TODO 设定当前detail 的issueId  不然会出事情
            console.log('onSuccess', data);
            refreshCommentDetailIssues(data, 'InvalidError');

            props?.taskItemRef?.current?.updateTaskItem(props?.taskId);
          },
        });
        break;
      case 'APPEAL_INVALID':
        appealModalRef?.current?.showAppeal({
          status: true,
          title: '填写驳回理由',
          detailId: commentItem?.detailId,
          type: info.key,
          onSuccess: async (data: any) => {
            // TODO 刷新 当前detail Id 下面的 issues
            // TODO 设定当前detail 的issueId  不然会出事情
            console.log('onSuccess', data);
            refreshCommentDetailIssues(data, 'ValidError');

            props?.taskItemRef?.current?.updateTaskItem(props?.taskId);
          },
        });
        break;
      case 'REVIEWEE_RESOLVE': {
        let data = {
          detailId: commentItem?.detailId,
        };
        let resolveResponse = await uniCommonService(
          'Api/Dmr/DmrCardQualityExamine/ResolveDetail',
          {
            method: 'POST',
            data: data,
          },
        );
        if (resolveResponse?.statusCode === 200) {
          // 更新当前detail status
          updateCommentItemPropsViaFormKey(commentItem?.formKey, {
            Status: 'Resolved',
          });

          props?.taskItemRef?.current?.updateTaskItem(props?.taskId);
        }
        break;
      }
      case 'APPEAL':
        appealModalRef?.current?.showAppeal({
          status: true,
          title: '填写申诉理由',
          detailId: commentItem?.detailId,
          type: info.key,
          onSuccess: async (data: any) => {
            // TODO 刷新 当前detail Id 下面的 issues
            // TODO 设定当前detail 的issueId  不然会出事情
            console.log('onSuccess', data);
            refreshCommentDetailIssues(data, 'Appealed');

            props?.taskItemRef?.current?.updateTaskItem(props?.taskId);
          },
        });
        break;
      default:
        break;
    }
  };

  const refreshCommentDetailIssues = async (data: any, status?: string) => {
    let currentDetailIssuesResponse: RespVO<IssueItem[]> =
      await getDetailIssuesReq([data?.issueId]);
    if (
      currentDetailIssuesResponse?.code === 0 &&
      currentDetailIssuesResponse?.statusCode === 200
    ) {
      let issueDetailItem = currentDetailIssuesResponse?.data?.at(0);

      // EventBy 换 userInfo
      for (let issueEventItem of issueDetailItem?.IssueEvents) {
        let userInfoResponse = await getDetailIssueEventUserInfo(
          issueEventItem?.EventBy,
        );
        issueEventItem['eventUserInfo'] = userInfoResponse?.data;
      }

      let currentCommentDetail = comments?.find(
        (item) => item?.detailId?.toString() === data?.detailId?.toString(),
      );
      if (!isEmptyValues(currentCommentDetail)) {
        currentCommentDetail['issueId'] = data?.issueId;
        currentCommentDetail['issueItem'] = issueDetailItem;

        if (!isEmptyValues(status)) {
          currentCommentDetail['Status'] = status;
          currentCommentDetail['originDetailItem']['Status'] = status;
        }

        setComments(comments?.slice());

        props?.onIssueUpdate && props.onIssueUpdate();
      }
    }
  };

  const { loading: getDetailIssuesLoading, run: getDetailIssuesReq } =
    useRequest(
      (issueIds) => {
        let data = {
          issueIds: issueIds,
        };

        return uniCommonService('Api/Pm/Issue/GetIssueDetails', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<IssueItem[]>) => {
          console.log('GetIssueDetails', response);
          return response;
        },
      },
    );

  const { run: getDetailIssueEventUserInfo } = useRequest(
    (userId) => {
      let data = {
        UserId: userId,
      };

      return uniCommonService('Api/Account/User/GetUserInfo', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        return response;
      },
    },
  );

  const onMouseEnterCommentItem = (
    event: any,
    key: string,
    uniqueId: string,
  ) => {
    props?.svgLinesContainerRef?.current?.setActiveEndId(
      `RULE-SCORE-ITEM-${key}-${uniqueId}`,
    );
  };

  const onMouseLeaveCommentItem = (
    event: any,
    key: string,
    uniqueId: string,
  ) => {
    props?.svgLinesContainerRef?.current?.setActiveEndId(undefined);
  };

  console.log(
    'Comments',
    orderBy(comments, ['position', 'y', 'x'], ['desc', 'asc', 'desc']),
  );

  const updateRuleItemOnSelect = async (commentItem: any, ruleCode: string) => {
    let ruleItem = props?.commentRuleDetails?.find(
      (item) => item?.RuleCode === ruleCode,
    );
    commentItem['ruleItem'] = ruleItem;
    setComments(comments?.slice());

    // 如果 存在detailId 调用接口
    if (!isEmptyValues(commentItem?.detailId)) {
      const scoreValue = form?.getFieldValue(
        `score-${commentItem?.formKey}-${commentItem?.uniqueId}`,
      );

      const remarkValue = form?.getFieldValue(
        `comment-${commentItem?.formKey}-${commentItem?.uniqueId}`,
      );

      let data = {
        taskId: props?.taskId,
        ruleCode: ruleCode,
        remark: remarkValue,
        inputScore: scoreValue,
        ColumnName: tableItemFeeItemSpecialKeyProcessor(commentItem?.formKey),
        detailId: commentItem?.detailId,
      };
      let upsertDetailResponse: RespVO<CommentDetailItem> =
        await uniCommonService(
          'Api/Dmr/DmrCardQualityExamine/UpsertRuleDetail',
          {
            method: 'POST',
            data: data,
          },
        );
    }
  };

  return (
    <Form
      id={'rule-score-comment-form'}
      form={form}
      style={{ overflowY: 'auto', height: '100%' }}
      onValuesChange={(changedValues, allValues) => {
        console.log('onValuesChange', changedValues, allValues);
      }}
      onScroll={(event) => {
        console.log('onScroll', event);
        props?.svgLinesContainerRef?.current?.onContentScroll();
      }}
    >
      <AppealModal containerRef={appealModalRef} />

      <Spin size={'large'} spinning={commentLoading}>
        <div
          id={'separator-comment-relative-container'}
          className={'separator-comment-relative-container'}
        >
          {isEmptyValues(comments) ? (
            <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
          ) : (
            <>
              {orderBy(
                comments,
                ['position', 'y', 'x'],
                ['desc', 'asc', 'desc'],
              )?.map((commentItem, index) => {
                const dropdownItems: any[] = (
                  props?.tableReadonly === true
                    ? props?.taskStatus === 'Rejected'
                      ? revieweeCommentOperationItems
                      : []
                    : ['Accepted', 'Error']?.includes(props?.taskStatus)
                    ? []
                    : reviewerCommentOperationItems
                )?.filter((item) => {
                  switch (item?.key) {
                    case 'DELETE':
                      return props?.taskStatus === 'Reviewing';
                    case 'RESOLVE':
                      return (
                        props?.taskStatus !== 'Reviewing' &&
                        props?.sysMasterItem?.ReviewSetting?.EnableResolve ===
                          true &&
                        commentItem?.originDetailItem?.Status === 'Failed'
                      );
                    case 'APPEAL_VALID':
                    case 'APPEAL_INVALID':
                      return (
                        props?.sysMasterItem?.ReviewSetting?.EnableAppeal ===
                          true &&
                        commentItem?.originDetailItem?.Status === 'Appealed'
                      );
                    case 'REVIEWEE_RESOLVE':
                      return (
                        props?.sysMasterItem?.ReviewSetting?.EnableAppeal ===
                          true &&
                        commentItem?.originDetailItem?.Status === 'Failed'
                        // (commentItem?.originDetailItem?.Status ===
                        //   'Failed' ||
                        //   commentItem?.originDetailItem
                        //     ?.Status === 'ValidError')
                      );
                    case 'APPEAL':
                      return (
                        props?.sysMasterItem?.ReviewSetting?.EnableAppeal ===
                          true &&
                        commentItem?.originDetailItem?.Status === 'Failed'
                      );
                    default:
                      return false;
                  }
                });

                return (
                  <div
                    id={`RULE-SCORE-ITEM-${commentItem?.formKey}-${commentItem?.uniqueId}`}
                    className={'grid-stack-item'}
                    style={{ margin: '10px 0px' }}
                  >
                    <div className={'grid-stack-item-content'}>
                      <div
                        className={`separator-comment-item separator-comment-item-${commentItem?.originDetailItem?.Status} rule-score-comment-item`}
                        onMouseEnter={(event) => {
                          onMouseEnterCommentItem(
                            event,
                            commentItem?.formKey,
                            commentItem?.uniqueId,
                          );
                        }}
                        onMouseLeave={(event) => {
                          onMouseLeaveCommentItem(
                            event,
                            commentItem?.formKey,
                            commentItem?.uniqueId,
                          );
                        }}
                        onClick={(event) => {
                          console.log('onClick');
                          // 滚动到顶上
                          let containerKey = commentItem?.containerKey;
                          let dmrContainerElement = document.getElementById(
                            commentItem?.containerKey,
                          );
                          if (!isEmptyValues(dmrContainerElement)) {
                            document
                              .getElementById('dmr-content-container')
                              ?.scrollTo({
                                top: dmrContainerElement?.offsetTop,
                                behavior: 'smooth',
                              });
                          }
                        }}
                      >
                        <div className={'status-tag-container'}>
                          <span className={'label'}>
                            {
                              QualityExamineDetailStatus?.[
                                commentItem?.originDetailItem?.['Status'] ??
                                  'Failed'
                              ]
                            }
                          </span>
                        </div>

                        <div className={'rule-score-item-container'}>
                          <div className={'rule-content-container'}>
                            {ruleScoreItemKeys
                              ?.filter((item) => {
                                if (item?.key === 'InputScore') {
                                  return (
                                    commentItem?.ruleItem?.AllowInputScore ===
                                    true
                                  );
                                }

                                return true;
                              })
                              ?.map((ruleItem) => {
                                return (
                                  <div className={'rule-score-item'}>
                                    <span className={'rule-score-label'}>
                                      {ruleItem?.label}：
                                    </span>
                                    {ruleItem?.key === 'OriginalInputValue' && (
                                      <span className={'rule-score-content'}>
                                        {commentItem?.originDetailItem
                                          ?.OriginalInputValue || '-'}
                                      </span>
                                    )}
                                    {ruleItem?.key === 'Creator' && (
                                      <span className={'rule-score-content'}>
                                        {
                                          commentItem?.originDetailItem
                                            ?.CreatorName
                                        }
                                      </span>
                                    )}
                                    {ruleItem?.key === 'DisplayErrMsg' && (
                                      <span
                                        className={'rule-score-content'}
                                        style={{ width: 160 }}
                                      >
                                        <UniSelect
                                          placeholder={'请选择规则'}
                                          dataSource={props?.commentRuleDetails}
                                          optionValueKey={'RuleCode'}
                                          showSearch={true}
                                          allowClear={false}
                                          disabled={
                                            props?.tableReadonly ||
                                            ![
                                              'Reviewing',
                                              'ReSubmitted',
                                            ]?.includes(props?.taskStatus)
                                          }
                                          value={
                                            commentItem?.ruleItem?.['RuleCode']
                                          }
                                          optionNameKey={'DisplayErrMsg'}
                                          onSelect={(value, record) => {
                                            // 更新 comment ruleItem
                                            updateRuleItemOnSelect(
                                              commentItem,
                                              value,
                                            );
                                          }}
                                        />
                                      </span>
                                    )}
                                    {ruleItem?.key === 'InputScore' && (
                                      <div className={'flex-row-center'}>
                                        <div
                                          style={{ width: 60 }}
                                          className={
                                            'rule-score-content rule-score-item-input'
                                          }
                                        >
                                          <RuleScoreCommentRestrictNumberInput
                                            form={form}
                                            needForm={true}
                                            formKey={commentItem.formKey}
                                            index={index}
                                            commentItem={commentItem}
                                            max={
                                              commentItem?.ruleItem?.TotalScore
                                            }
                                            tableReadonly={props?.tableReadonly}
                                            taskStatus={props?.taskStatus}
                                            step={
                                              commentItem?.ruleItem
                                                ?.AllowMultipleErrItems === true
                                                ? commentItem?.ruleItem?.Score
                                                : 1
                                            }
                                            taskId={props?.taskId}
                                            commentRuleDetails={
                                              props?.commentRuleDetails
                                            }
                                            onCommentItemDelete={
                                              onCommentItemDelete
                                            }
                                            onCommentItemUpdate={
                                              onCommentItemUpdate
                                            }
                                            updateCommentItemProps={
                                              updateCommentItemPropsViaFormKey
                                            }
                                          />
                                        </div>
                                        <span
                                          className={'rule-score-content'}
                                          style={{
                                            marginLeft: 5,
                                            color: '#1464f8',
                                          }}
                                        >
                                          {/^\d+$/.test(
                                            commentItem?.ruleItem?.[
                                              'scoreLabel'
                                            ]?.trim(),
                                          )
                                            ? `总分：${
                                                commentItem?.ruleItem?.[
                                                  'scoreLabel'
                                                ]?.trim() || ''
                                              }`
                                            : `规则：${
                                                commentItem?.ruleItem?.[
                                                  'scoreLabel'
                                                ]?.trim() || ''
                                              }`}
                                        </span>
                                      </div>
                                    )}
                                    {ruleItem?.key === 'Remark' && (
                                      <div
                                        style={{ flex: 1 }}
                                        className={
                                          'rule-score-content rule-score-item-input'
                                        }
                                      >
                                        <RuleScoreCommentContent
                                          form={form}
                                          formKey={commentItem.formKey}
                                          commentItem={commentItem}
                                          taskId={props?.taskId}
                                          commentRuleDetails={
                                            props?.commentRuleDetails
                                          }
                                          onCommentItemDelete={
                                            onCommentItemDelete
                                          }
                                          onCommentItemUpdate={
                                            onCommentItemUpdate
                                          }
                                          updateCommentItemProps={
                                            updateCommentItemPropsViaFormKey
                                          }
                                          tableReadonly={props?.tableReadonly}
                                          taskStatus={props?.taskStatus}
                                        />
                                      </div>
                                    )}
                                  </div>
                                );
                              })}
                          </div>

                          <div className={'detail-operation-container'}>
                            {dropdownItems?.map((item) => {
                              return (
                                <Button
                                  icon={item?.icon}
                                  type={'default'}
                                  size={'small'}
                                  onClick={() => {
                                    onContextMenuItemClick(
                                      { key: item?.key },
                                      commentItem,
                                    );
                                  }}
                                >
                                  {item?.label}
                                </Button>
                              );
                            })}
                          </div>
                        </div>

                        {!isEmptyValues(
                          commentItem?.issueItem?.IssueEvents,
                        ) && (
                          <div className={'issue-items-container'}>
                            <span className={'container-label'}>
                              反馈流程：
                            </span>
                            {commentItem?.issueItem?.IssueEvents?.map(
                              (issueEventItem) => {
                                const isCoder =
                                  globalState?.dictData?.['Coder']?.find(
                                    (item) => {
                                      return (
                                        item?.Code ===
                                        issueEventItem?.eventUserInfo
                                          ?.EmployeeCode
                                      );
                                    },
                                  ) !== undefined;

                                return (
                                  <div
                                    style={
                                      isCoder
                                        ? { marginLeft: '40%' }
                                        : { marginRight: '40%' }
                                    }
                                    className={'issue-item'}
                                  >
                                    <span className={'issue-item-content'}>
                                      {issueEventItem?.EventByName}：
                                      {issueEventItem?.Comment}
                                    </span>
                                  </div>
                                );
                              },
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </>
          )}
        </div>
      </Spin>
    </Form>
  );
};

interface RuleScoreCommentContentProps {
  form?: any;
  formKey?: string;
  commentItem?: any;
  taskId?: string;
  onCommentItemDelete?: (formKey: string, uniqueId: string) => void;
  onCommentItemUpdate?: (
    formKey: string,
    detailId?: string | number,
    uniqueId?: string,
    type?: string,
    updateKey?: string,
  ) => void;
  updateCommentItemProps?: any;

  commentRuleDetails?: RuleItem[];

  tableReadonly?: boolean;
  taskStatus?: string;

  ignoreUpdate?: boolean;
  updateAnnotationContent?: (value) => void;
}

export const RuleScoreCommentContent = (
  props: RuleScoreCommentContentProps,
) => {
  const updateCommentDetail = async (remark: any) => {
    const ruleCode = props?.commentItem?.ruleItem?.RuleCode;

    const scoreValue = props?.form?.getFieldValue(
      `score-${props?.formKey}-${props?.commentItem?.uniqueId}`,
    );

    if (isEmptyValues(ruleCode)) {
      message.warn('未配置 规则，请检查规则');

      return;
    }

    setTimeout(async () => {
      // TODO data
      let data = {
        taskId: props?.taskId,
        ruleCode: ruleCode,
        remark: remark,
        inputScore: scoreValue,
        ColumnName: tableItemFeeItemSpecialKeyProcessor(props?.formKey),
      };
      if (!isEmptyValues(props?.commentItem?.detailId)) {
        data['detailId'] = props?.commentItem?.detailId;
      }
      let upsertCategoryDetailResponse: RespVO<any> = await uniCommonService(
        'Api/Dmr/DmrCardQualityExamine/UpsertRuleDetail',
        {
          method: 'POST',
          data: data,
        },
      );

      if (
        upsertCategoryDetailResponse?.code === 0 &&
        upsertCategoryDetailResponse?.statusCode === 200
      ) {
        props?.form?.setFieldValue(
          `comment-${props?.formKey}-${upsertCategoryDetailResponse?.data}`,
          remark,
        );

        props?.onCommentItemUpdate &&
          props?.onCommentItemUpdate(
            props?.formKey,
            upsertCategoryDetailResponse?.data,
            props?.commentItem?.uniqueId,
            'comment',
            'Remark',
          );
      }
    }, 0);
  };

  return (
    <div className={'detail-comment-content-container'}>
      <div className={'comment-input-container'}>
        <Form.Item
          name={`comment-${props?.formKey}-${props?.commentItem?.uniqueId}`}
        >
          <TextArea
            id={`COMMENT-INPUT-${props?.formKey}-${props?.commentItem?.uniqueId}`}
            showCount={false}
            autoSize={true}
            maxLength={-1}
            style={{ minHeight: 60, resize: 'none', width: 160 }}
            disabled={
              props?.tableReadonly ||
              !['Reviewing', 'ReSubmitted']?.includes(props?.taskStatus)
            }
            placeholder="请输入理由"
            onFocus={(event) => {
              if (props?.tableReadonly === true) {
                return;
              }
              setTimeout(() => {
                props?.updateCommentItemProps &&
                  props?.updateCommentItemProps(props?.formKey, {
                    editMode: true,
                  });
              }, 10);
            }}
            onChange={(event) => {
              if (props?.updateAnnotationContent) {
                props?.updateAnnotationContent(event?.target?.value);
              }
            }}
            onBlur={(event) => {
              if (props?.tableReadonly === true) {
                return;
              }

              if (props?.ignoreUpdate !== true) {
                updateCommentDetail(event?.target?.value);
              }
            }}
          />
        </Form.Item>
      </div>
    </div>
  );
};

interface RuleScoreCommentRestrictNumberInputProps {
  form?: any;
  formKey: any;
  min?: number;
  max?: number;

  step?: number;
  suffix?: string;

  index?: number;
  commentItem?: any;
  onChange?: any;
  value?: any;

  tableReadonly?: boolean;
  taskStatus?: string;

  commentRuleDetails?: RuleItem[];
  taskId?: string;
  onCommentItemDelete?: (formKey: string, uniqueId: string) => void;
  onCommentItemUpdate?: (
    formKey: string,
    detailId?: string | number,
    uniqueId?: string,
    type?: string,
    updateKey?: string,
  ) => void;
  updateCommentItemProps?: any;

  ignoreUpdate?: boolean;
  updateAnnotationContent?: (value) => void;

  extraValue?: any;
  needForm?: boolean;
}

export const RuleScoreCommentRestrictNumberInput = (
  props: RuleScoreCommentRestrictNumberInputProps,
) => {
  const updateCommentScore = async (score: any) => {
    const ruleCode = props?.commentItem?.ruleItem?.RuleCode;

    const remarkValue = props?.form?.getFieldValue(
      `comment-${props?.formKey}-${props?.commentItem?.uniqueId}`,
    );

    if (isEmptyValues(ruleCode)) {
      message.warn('未配置 规则，请检查规则');

      return;
    }

    setTimeout(async () => {
      // TODO data
      let data = {
        taskId: props?.taskId,
        ruleCode: ruleCode,
        inputScore: score,
        ColumnName: tableItemFeeItemSpecialKeyProcessor(props?.formKey),
        Remark: remarkValue ?? '',
      };
      if (!isEmptyValues(props?.commentItem?.detailId)) {
        data['detailId'] = props?.commentItem?.detailId;
      }
      let upsertCategoryDetailResponse: RespVO<any> = await uniCommonService(
        'Api/Dmr/DmrCardQualityExamine/UpsertRuleDetail',
        {
          method: 'POST',
          data: data,
        },
      );

      if (
        upsertCategoryDetailResponse?.code === 0 &&
        upsertCategoryDetailResponse?.statusCode === 200
      ) {
        props?.form?.setFieldValue(
          `score-${props?.formKey}-${upsertCategoryDetailResponse?.data}`,
          score,
        );

        props?.onCommentItemUpdate &&
          props?.onCommentItemUpdate(
            props?.formKey,
            upsertCategoryDetailResponse?.data,
            props?.commentItem?.uniqueId,
            'score',
            'InputScore',
          );
      }
    }, 0);
  };

  const Wrapper = props?.needForm === true ? Form.Item : React.Fragment;

  return (
    <div className={'remove-arrow'}>
      <Wrapper name={`score-${props?.formKey}-${props?.commentItem?.uniqueId}`}>
        <Input
          {...(props?.extraValue ?? {})}
          style={{ textAlign: 'center' }}
          id={`RULE-SCORE-INPUT-${props?.formKey}-${props?.commentItem?.uniqueId}`}
          min={props?.min ?? 0}
          max={props?.max}
          className={'restrict-input-number-container'}
          step={props?.step || 1}
          suffix={props?.suffix || undefined}
          placeholder={'分数'}
          disabled={
            props?.tableReadonly ||
            !['Reviewing', 'ReSubmitted']?.includes(props?.taskStatus)
          }
          onKeyDown={(event) => {
            let extraKeys = [];
            if (props?.step !== 1) {
              // 指定precious 并且 precious为0 允许 dot
              extraKeys.push('.');
            }

            numberInputRestrictKeyDown(event, extraKeys);
          }}
          onChange={(event) => {
            let value = event.target.value;

            if (value) {
              if (props?.step !== 1) {
                let valuesWithoutDot = value.split('.');
                if (!isEmptyValues(valuesWithoutDot?.[1])) {
                  if (decimalMap?.[props?.step]) {
                    if (
                      !decimalMap?.[props?.step]?.includes(
                        valuesWithoutDot?.[1],
                      )
                    ) {
                      value = valuesWithoutDot[0];
                    }
                  } else {
                    value = valuesWithoutDot[0];
                  }
                }
              }

              // 最大最小值
              if (props?.max) {
                if (parseFloat(value) > props.max) {
                  value = props?.max.toString();
                }
              }

              if (parseFloat(value) < (props.min ?? 0)) {
                value = (props?.min ?? 0).toString();
              }
            }

            if (/^-.*$/.test(value?.toString())) {
              value = '';
            }

            // TODO 接口更新数据
            if (props?.ignoreUpdate !== true) {
              updateCommentScore(value);
            }

            if (props?.updateAnnotationContent) {
              props?.updateAnnotationContent(value);
            }
          }}
        />
      </Wrapper>
    </div>
  );
};

export default RuleScoreComment;
