.form-content-item-container {
  position: relative;

  .other-hospital-item {
    .select {
      width: 100%;

      .ant-select-selector {
        border: 1px solid #d9d9d9;
        border-radius: 6px;

        &:hover {
          border-color: #40a9ff;
        }
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      .ant-select-selection-search-input {
        height: 100%;
      }
    }
  }

  // 错误状态样式
  .ant-tooltip-open {
    .select .ant-select-selector {
      border-color: #ff4d4f;

      &:hover {
        border-color: #ff4d4f;
      }
    }

    &.ant-select-focused .select .ant-select-selector {
      border-color: #ff4d4f;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  }
}

// 入院途径和机构名称联动组件样式
.admission-path-type-container {
  display: flex;
  flex: 1;
  // flex-direction: column;

  .admission-path-row-wrap {
    display: flex;
    align-items: center;
    flex-flow: row nowrap;
  }

  .prefix {
    white-space: nowrap;
    margin-right: 5px;
  }

  // 移除容器级别的border样式，参照OutType的做法
  .input-container {
    border-bottom: 0px solid transparent;

    &:focus,
    &:active,
    &:hover {
      border-bottom: 0px solid transparent;
    }
  }

  .select-container:hover {
    border-bottom: 0px solid transparent !important;
  }

  .primary-input {
    max-width: 300px;
  }

  .transfer-hospital-item {
    display: flex;
    align-items: center;
    flex: 1;
    margin-left: 10px;

    .label {
      white-space: nowrap !important;
      margin-right: 10px;
    }

    .hospital-select {
      width: 100%;
      min-width: 200px;
      border-bottom: 0px solid transparent;

      &:focus,
      &:active,
      &:hover {
        border-bottom: 0px solid transparent;
      }

      // 只保留禁用状态的基本样式，让Antd自己处理focus
      &.ant-select-disabled {
        .ant-select-selection-placeholder {
          color: rgba(0, 0, 0, 0.25);
        }
      }
    }
  }

  // 错误状态的tooltip样式保持，但简化
  .ant-tooltip-open {
    .hospital-select .ant-select-selector {
      border-color: #ff4d4f;

      &:hover {
        border-color: #ff4d4f;
      }
    }

    &.ant-select-focused .hospital-select .ant-select-selector {
      border-color: #ff4d4f;
      box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
    }
  }
}
