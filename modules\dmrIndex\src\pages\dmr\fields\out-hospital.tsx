import { IGridItem } from '@/pages/dmr/interfaces';
import { SuffixItem } from '@uni/grid/src/components/suffix-item';

export const outHospital: IGridItem[] = [
  {
    data: {
      prefix: '出院时间',
      key: 'OutDate',
      desc: '',
      suffix: '',
      // component: 'DatePicker',
      component: 'DateSelect',
      props: {
        type: 'compact',
        formKey: 'OutDate',
        style: {
          width: '100%',
        },
        className: 'border-none',
        datePicker: false,
        showHours: true,
        showSeconds: true,
        calculateItems: [
          {
            needCalculateFormKey: 'InPeriod',
            calculateFromFormKeys: ['InDate', 'OutDate'],
            dateCalculateUnits: 'day',
          },
        ],
      },
    },
    w: 4,
    md: {
      w: 5,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '出院科别',
      key: 'OutDept',
      desc: '',
      suffix: '',
      component: 'DmrSelect',
      props: {
        formKey: 'OutDept',
        className: 'department-container',
        modelDataKey: 'CliDepts',
        placeholder: '请选择',
        optionNameKey: 'Name',
      },
    },
    w: 4,
    sm: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '病房',
      key: 'OutWard',
      desc: '',
      suffix: '',
      component: 'Input',
      props: {
        bordered: false,
      },
    },
    w: 3,
    sm: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '实际住院',
      key: 'InPeriod',
      desc: '',
      suffix: '天',
      component: 'RestrictInputNumber',
      props: {
        disabled: true,
        min: 1,
        step: 1,
        precious: 0,
        formKey: 'InPeriod',
        className: 'border-none',
      },
    },
    offsetX: 1,
    w: 3,
    sm: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
];

export const outHospitalType: IGridItem[] = [
  {
    data: {
      prefix: '',
      key: 'OutHospital',
      desc: '',
      suffix: '',
      component: 'OutType',
      props: {
        className: 'border-none',
        style: {
          border: 'none',
        },

        modelDataKey: 'LYFS',
        modelDataGroup: 'Dmr',
        labelMapping: {
          '1': '1.医嘱离院',
          '2': '2.医嘱转院，拟接收医疗机构名称：',
          '3': '3.医嘱转社区卫生服务机构/乡镇卫生院，拟接收医疗机构名称：',
          '4': '4.非医嘱离院',
          '5': '5.死亡',
          '9': '9.其他',
        },
        hospInputType: 'text',
      },
    },
    h: 2,
    w: 24,
    md: {
      h: 3.3,
    },
    sm: {
      h: 3.3,
    },
    xs: {
      h: 4.6,
    },
    xxs: {
      h: 5.5,
    },
  },
];
