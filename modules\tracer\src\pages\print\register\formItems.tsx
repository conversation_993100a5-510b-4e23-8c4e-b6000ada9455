import { Emitter } from '@uni/utils/src/emitter';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { Button, Col, Form, Image, Row, Select } from 'antd';
import { PRINT_EVENTS } from '../eventConstants';

export const PrintFormItems = (dictData, cameraPics) => [
  {
    dataType: 'text',
    name: 'Applicant',
    title: '来复印者', //委托人
    placeholder: '请输入',
    colProps: { span: 8 },
  },
  {
    dataType: 'select',
    name: 'ApplicantType',
    title: '用户类别',
    placeholder: '请选择',
    opts: dictData?.['Mr']?.ApplicantType,
    colProps: { span: 8 },
  },
  {
    dataType: 'select',
    name: 'ApplicantRelation',
    title: '与患者关系',
    placeholder: '请选择',
    opts: dictData?.['Mr']?.ApplicantRealtion,
    colProps: { span: 8 },
    rules: [{ required: true, message: '请选择' }],
  },
  {
    dataType: 'select',
    name: 'ApplicantDepartment',
    title: '复印单位',
    placeholder: '请选择',
    opts: dictData?.['Mr']?.ApplicantDepartment,
    colProps: { span: 8 },
    rules: [{ required: true, message: '请选择' }],
  },
  {
    name: 'ApplicantIdCardType',
    colProps: { span: 8 },
    render: (
      <Col span={8}>
        <Form.Item
          name="ApplicantIdCardType"
          label="证件材料"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
        >
          <Select
            placeholder="请选择"
            style={{ width: 'calc(100% - 95px)', marginRight: '7px' }}
            fieldNames={{ label: 'Name', value: 'Code' }}
            filterOption={(inputValue, option) => {
              return (
                (option &&
                  option.label
                    ?.toString()
                    ?.toLowerCase()
                    ?.indexOf(inputValue) !== -1) ||
                option.value?.toString()?.toLowerCase()?.indexOf(inputValue) !==
                  -1 ||
                pinyinInitialSearch(
                  option.label?.toString()?.toLowerCase(),
                  inputValue.toLowerCase(),
                )
              );
            }}
            options={dictData?.['Mr']?.ApplicantIdCardType}
            onChange={(value) => {
              Emitter.emit(PRINT_EVENTS.APPLICANT_ID_CARD_TYPE_CHANGE, value);
            }}
          />
          <Button
            style={{ width: '88px' }}
            onClick={(e) => {
              (global?.window as any)?.eventEmitter?.emit(
                'CAMERA_STATUS_CHANGE',
                {
                  status: true,
                  onCameraOut: (pics) => {
                    console.log('CAMERA_STATUS_CHANGE Camera Out', pics);
                    // 这边的 在关闭的时候只会回传当前还在pic list里面的图的 data 复印登记需要记录
                    Emitter.emit(PRINT_EVENTS.CAMERA_OUT, pics);
                  },
                },
              );
            }}
          >
            证件拍照
          </Button>
        </Form.Item>
      </Col>
    ),
  },
  {
    dataType: 'text',
    name: 'ApplicantIdCard',
    title: '证件号码',
    placeholder: '请输入',
    colProps: { span: 8 },
    rules: [{ required: true, message: '请输入' }],
  },
  {
    dataType: 'text',
    name: 'ApplicantTel',
    title: '联系电话',
    placeholder: '请输入',
    colProps: { span: 8 },
    rules: [
      { required: true, message: '请输入' },
      {
        validator: (rule, value) => {
          const regExp = /^1[3-9]\d{9}$/;
          if (value && !regExp.test(value)) {
            return Promise.reject('请输入有效的中国大陆手机号码');
          }
          return Promise.resolve();
        },
      },
    ],
  },
  {
    dataType: 'custom',
    hidden: cameraPics?.length < 1,
    render: (
      <Col span={8}>
        <Row>
          <Col span={18} offset={6}>
            <Row style={{ width: '100%' }}>
              <Col xxl={6} md={8}>
                <Image
                  width={80}
                  preview={{ visible: false }}
                  src={cameraPics?.at(0)?.imgSrc}
                  onClick={() => Emitter.emit(PRINT_EVENTS.CAMERA_PICS_VIEW)}
                />
              </Col>
              {cameraPics?.length > 1 && (
                <Col xxl={6} md={8}>
                  <Image
                    width={80}
                    preview={{ visible: false }}
                    src={cameraPics?.at(1)?.imgSrc}
                    onClick={() => Emitter.emit(PRINT_EVENTS.CAMERA_PICS_VIEW)}
                  />
                </Col>
              )}
              {cameraPics?.length > 2 && (
                <Col xxl={6} md={8}>
                  <Button
                    type="dashed"
                    style={{ width: '88px', height: '45px', borderRadius: '0' }}
                    onClick={() => Emitter.emit(PRINT_EVENTS.CAMERA_PICS_VIEW)}
                  >
                    查看更多
                  </Button>
                </Col>
              )}
            </Row>
          </Col>
        </Row>
      </Col>
    ),
  },
  {
    dataType: 'text',
    name: 'ApplicantAddress',
    title: '联系地址',
    placeholder: '请输入',
    colProps: { span: 8 },
  },
  {
    dataType: 'number',
    name: 'CopyCnt',
    title: '复印份数',
    colProps: { span: 8 },
    placeholder: '请输入',
    fieldProps: {
      min: 1,
    },
    rules: [{ required: true, message: '请输入' }],
  },
  {
    dataType: 'select',
    name: 'Reason',
    title: '复印目的',
    fieldProps: {
      fieldNames: { label: 'Name', value: 'Code' },
    },
    placeholder: '请选择',
    opts: dictData?.['Mr']?.PrintReason,
    colProps: { span: 8 },
    rules: [{ required: true, message: '请选择' }],
  },
  {
    dataType: 'select',
    name: 'PrintDocType',
    title: '复印内容',
    fieldProps: {
      fieldNames: { label: 'Name', value: 'Code' },
      mode: 'multiple',
      maxTagCount: 3,
    },
    placeholder: '请选择',
    opts: dictData?.['Mr']?.PrintDocType,
    colProps: { span: 8 },
    rules: [{ required: true, message: '请选择' }],
  },
  {
    dataType: 'date',
    name: 'PrintDate',
    title: '复印时间',
    colProps: { span: 8 },
    placeholder: '请选择',
    fieldProps: {
      style: { width: '100%' },
    },
  },
  {
    dataType: 'text',
    name: 'ApplicantIdCardStorage',
    title: '证件存放处',
    placeholder: '请输入',
    colProps: { span: 8 },
  },
  {
    dataType: 'number',
    name: 'PrintFee',
    title: '复印金额',
    placeholder: '请输入',
    colProps: { span: 8 },
    fieldProps: {
      min: 0,
      precision: 2,
    },
  },
];
