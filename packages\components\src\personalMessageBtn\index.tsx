import React, { useState, useRef, useEffect } from 'react';
import {
  Layout,
  Menu,
  notification,
  Form,
  Input,
  Dropdown,
  Badge,
  Space,
  Button,
  message,
  List,
  Tooltip,
  Modal,
  TreeSelect,
} from 'antd';
import {
  UserOutlined,
  NotificationOutlined,
  UnlockOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  LogoutOutlined,
  HomeOutlined,
  ChromeOutlined,
  MailOutlined,
} from '@ant-design/icons';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { HeaderMenuItem, MenuData } from '../menu-sider/MenuSider';
import intersection from 'lodash/intersection';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { getUserInfo } from '@uni/services/src/userService';
import cloneDeep from 'lodash/cloneDeep';
import { getAllLeafMenuItem, isEmptyValues } from '@uni/utils/src/utils';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';
import { useMount } from 'ahooks';
import './index.less';

const colorSet = (type) => {
  switch (type) {
    case '0':
    default:
      return '#7495f7';
    case '2':
      return '#48d12b';
    case '3':
      return '#f7b072';
    case '4':
      return '#ff3232';
  }
};

const useInterval = (cb: Function, delay: number) => {
  const callback = useRef<Function>(() => {});
  useEffect(() => {
    callback.current = cb;
  });

  useEffect(() => {
    if (delay !== null) {
      const interval = setInterval(() => callback.current(), delay || 5000);
      return () => clearInterval(interval);
    }
    return null;
  }, [delay]);
};

export const personalMessagesUrlOpen = () => {
  if (global?.window?.location?.pathname?.indexOf('/personalMessages') > -1) {
    return;
  }
  global?.window?.open('/personalMessages/index');
};

export const personalMessages = () => {
  if (global?.window?.location?.pathname?.indexOf('/personalMessages') > -1) {
    return;
  }
  global?.window?.open('/personalMessages/index');
};

interface IPersonalMessagesHeaderBtnProps {}

const PersonalMessagesHeaderBtn = (props: IPersonalMessagesHeaderBtnProps) => {
  const noti = useRef(null);
  const [totalMessages, setTotalMessages] = useState([]);
  const [modalState, setModalState] = useState({
    visible: false,
    record: null,
  });

  const fetchMessages = async () => {
    let apiRes = await uniCommonService('Api/Pm/PmQuery/GetPersonalMessages', {
      method: 'POST',
      data: {
        IsNotRead: true,
      },
    });
    if (apiRes?.code === 0 && apiRes?.statusCode === 200) {
      setTotalMessages(apiRes.data);
    }
  };

  const setMessageRead = async (ids: (number | string)[]) => {
    let apiRes = await uniCommonService('Api/Pm/PmOp/SetRead', {
      method: 'POST',
      data: {
        PmIds: ids,
      },
    });

    if (apiRes?.code === 0 && apiRes?.statusCode === 200) {
      fetchMessages();
    }
  };

  const handleMessageBtnClk = () => {
    notification.open({
      key: 'global',
      message: '未读消息',
      top: 10,
      onClose: () => {
        fetchMessages();
      },
      description: (
        <>
          <List
            itemLayout="horizontal"
            className="messages_noti_list"
            dataSource={totalMessages?.filter((d) => !d.IsRead) || []}
            renderItem={(item: any) => {
              const content = item.Content || '';
              const parts = content.split('||');
              const hasUrl = parts.length > 1 && parts[1];

              return (
                <List.Item
                  className="messages_noti_item"
                  onClick={(e) => {
                    e.stopPropagation();
                    setModalState({
                      visible: true,
                      record: item,
                    });
                  }}
                >
                  <List.Item.Meta
                    avatar={
                      item.MsgLevel === '4' ? (
                        <CloseCircleOutlined
                          style={{ color: colorSet(item.MsgLevel) }}
                        />
                      ) : item.MsgLevel === '2' ? (
                        <CheckCircleOutlined
                          style={{ color: colorSet(item.MsgLevel) }}
                        />
                      ) : item.MsgLevel === '3' ? (
                        <ExclamationCircleOutlined
                          style={{ color: colorSet(item.MsgLevel) }}
                        />
                      ) : (
                        <InfoCircleOutlined
                          style={{ color: colorSet(item.MsgLevel) }}
                        />
                      )
                    }
                    title={
                      <span style={{ color: colorSet(item.MsgLevel) }}>
                        {item.Title}
                      </span>
                    }
                    description={
                      <div>
                        <div>{parts[0]}</div>
                        {hasUrl && (
                          <Button
                            type="link"
                            size="small"
                            style={{
                              padding: '4px 8px',
                              height: 'auto',
                              marginTop: '4px',
                              color: '#7495f7',
                            }}
                            onClick={async (e) => {
                              e.stopPropagation();
                              // 先标记消息为已读
                              await setMessageRead([item.Id]);
                              // 然后打开链接
                              const baseUrl = `${window.location.protocol}//${window.location.host}`;
                              const fullUrl = baseUrl + parts[1];
                              window.open(fullUrl, '_blank');
                            }}
                          >
                            查看详情 →
                          </Button>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              );
            }}
          />
          <div className="messages_noti_footer">
            {/* <span
              onClick={(e) => {
                e.stopPropagation();
                notification.close('global');
                fetchMessages();
                setTimeout(() => {
                  personalMessagesUrlOpen();
                }, 200);
              }}
              style={{ cursor: 'pointer', color: '#7495f7' }}
            >
              查看全部消息
            </span> */}
            <span></span>
            <span
              style={{
                cursor: 'pointer',
                color:
                  totalMessages?.filter((d) => !d.IsRead)?.length > 0
                    ? '#7495f7'
                    : '#aaa',
              }}
              onClick={(e) => {
                if (totalMessages?.filter((d) => !d.IsRead)?.length > 0) {
                  setMessageRead(
                    totalMessages?.filter((d) => !d.IsRead)?.map((d) => d.Id),
                  );
                }
              }}
            >
              全部已读
            </span>
          </div>
        </>
      ),
      className: 'custom-class',
      duration: null,
    });
  };

  // 轮询
  useInterval(() => {
    fetchMessages();
  }, 1000 * 60);
  // 首次
  useMount(() => {
    fetchMessages();
  });

  return (
    <Badge count={totalMessages?.filter((d) => !d.IsRead)?.length || 0}>
      <Tooltip title={'私信'}>
        <MailOutlined ref={noti} onClick={handleMessageBtnClk} />
      </Tooltip>
    </Badge>
  );
};

export default PersonalMessagesHeaderBtn;
