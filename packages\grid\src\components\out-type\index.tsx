/**
 * 病案首页 离院方式 组件
 */
import React, { useEffect } from 'react';
import './index.less';
import { Col, Form, Input, Row } from 'antd';
import { SuffixItem } from '../suffix-item';
import { AutoSelect } from '../auto-select';
import { RequireMark } from '../require-mark';
// @ts-ignore
import { useModel } from '@@/plugin-model/useModel';

interface OutTypeProps {
  form: any;
  value?: string;

  onChange?: (value: string) => void;

  modelDataGroup?: string;
  modelDataKey?: string;

  labelMapping: {
    [key: string]: string;
  };
  hospInputType?: string;
}

export const OutType = (props: OutTypeProps) => {
  const [suffixItems, setSuffixItems] = React.useState<any[]>([]);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  useEffect(() => {
    if (props?.modelDataKey && props?.modelDataGroup) {
      setSuffixItems(
        globalState?.dictData?.[props?.modelDataGroup]?.[props?.modelDataKey] ??
          [],
      );
    }
  }, [globalState]);

  return (
    <div className={'out-type-container'}>
      <Row>
        <div className={'out-type-row-wrap'}>
          <label className={'prefix'}>
            <RequireMark />
            离院方式
          </label>
          <Form.Item name={'OutType'}>
            <AutoSelect
              className={`input-container primary-input form-content-item-container`}
              formItemId={`formItem#OutType`}
              dataSource={suffixItems}
              formKey={'OutType'}
              width={290}
              form={props?.form}
              value={props?.form?.getFieldValue('OutType')}
              onChange={(value) => {
                if (['1', '4', '5', '9'].includes(value)) {
                  props?.form.setFieldValue('AcceptHosp', '');
                  props?.form.setFieldValue('AcceptCommHosp', '');
                }
              }}
            />
          </Form.Item>

          {/*<Input*/}
          {/*  className="input-container primary-input"*/}
          {/*  bordered={false}*/}
          {/*  value={preValueProcessor(props?.value)}*/}
          {/*  onChange={(event) => {*/}
          {/*    // TODO  这个办法很僵硬 建议后期改掉 不要使用 此处仅运行code输入才有效 非code内的 全部置为空*/}
          {/*    event.target.value = parseValueWithItemsCodes(event.target.value);*/}
          {/*    props?.onChange && props?.onChange(event.target.value);*/}
          {/*  }}*/}
          {/*/>*/}

          <SuffixItem
            style={{ marginLeft: 10 }}
            label={
              props?.labelMapping?.['1'] ??
              suffixItems?.find((item) => item?.Code === '1')?.Name
            }
            showInput={false}
          />
        </div>
        <SuffixItem
          className={'accept-hosp'}
          style={{ flex: 1, flexFlow: 'row' }}
          label={
            props?.labelMapping?.['2'] ??
            suffixItems?.find((item) => item?.Code === '2')?.Name
          }
          showInput={props?.hospInputType === 'text' && true}
          showSearch={props?.hospInputType === 'search' && true}
          formKey={'AcceptHosp'}
          formItemId={'formItem#AcceptHosp'}
          conditionKey={'OutType'}
          disableCondition={(value) => {
            return value?.toString() !== '2';
          }}
        />
      </Row>

      <Row>
        <Col span={14}>
          <SuffixItem
            style={{ flex: 1, flexFlow: 'row wrap' }}
            label={
              props?.labelMapping?.['3'] ??
              suffixItems?.find((item) => item?.Code === '3')?.Name
            }
            showInput={props?.hospInputType === 'text' && true}
            showSearch={props?.hospInputType === 'search' && true}
            formKey={'AcceptCommHosp'}
            formItemId={'formItem#AcceptCommHosp'}
            conditionKey={'OutType'}
            disableCondition={(value) => {
              return value?.toString() !== '3';
            }}
          />
        </Col>
        <Col span={10}>
          <div className={'flex-row-center'} style={{ height: '100%' }}>
            <SuffixItem
              label={
                props?.labelMapping?.['4'] ??
                suffixItems?.find((item) => item?.Code === '4')?.Name
              }
              showInput={false}
            />
            <SuffixItem
              label={
                props?.labelMapping?.['5'] ??
                suffixItems?.find((item) => item?.Code === '5')?.Name
              }
              showInput={false}
            />
            <SuffixItem
              label={
                props?.labelMapping?.['9'] ??
                suffixItems?.find((item) => item?.Code === '9')?.Name
              }
              showInput={false}
            />
          </div>
        </Col>
      </Row>
    </div>
  );
};
