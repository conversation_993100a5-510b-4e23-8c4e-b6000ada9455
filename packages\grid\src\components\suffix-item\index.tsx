import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import { Input, Form, Select, Spin, Tooltip } from 'antd';
// @ts-ignore
import { useModel } from '@@/plugin-model/useModel';
import { UniAntdSelect, UniSelect } from '@uni/components/src';
import {
  triggerFormValueChangeEvent,
  getSelectorDropdownContainerNode,
} from '../../utils';
import UniDmrSelect from '../dmr-select/UniDmrSelect';
import { isEmptyValues } from '@uni/utils/src/utils';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { useDebounceFn } from 'ahooks';

const { Option } = UniAntdSelect;

const inputFocusNotSelectAll =
  (window as any).externalConfig?.['dmr']?.inputFocusNotSelectAll ?? false;

const tableSelectorDropdownHeight = (window as any).externalConfig?.['dmr']
  ?.tableSelectorDropdownHeight;

export interface OtherHospitalItem {
  HospCode: string;
  HospName: string;
  InsurOrganizationCode?: string;
  InsurOrganizationName?: string;
  IsValid: boolean;
}

export interface SuffixItemProps {
  formItemId?: string;
  className?: string;
  style?: React.CSSProperties;
  label: string;
  value?: string;
  showInput?: boolean;
  showTags?: boolean;
  showSearch?: boolean;
  formKey?: string;

  disableCondition?: (value: any) => boolean;

  conditionKey?: string;

  modelDataKey?: string;
  modelDataGroup?: string;
}

export const SuffixItem = (props: SuffixItemProps) => {
  const [selectDataSource, setSelectDataSource] = useState([]);
  const [searchDataSource, setSearchDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [errorTooltipOpen, setErrorTooltipOpen] = useState(false);

  const [suffixSelectStatus, setSuffixSelectStatus] = useState(false);

  const form = Form.useFormInstance();

  const { globalState, setQiankunGlobalState } = useModel(
    '@@qiankunStateFromMaster',
  );

  const dictData = props?.modelDataGroup
    ? globalState?.dictData?.[props?.modelDataGroup]
    : globalState?.dictData;

  let conditionValue = undefined;
  if (props?.conditionKey) {
    conditionValue = Form.useWatch(props?.conditionKey, form);
  }

  useEffect(() => {
    if (props?.modelDataKey) {
      if (dictData?.[props?.modelDataKey]) {
        setSelectDataSource(dictData[props?.modelDataKey]);
      }
    }
  }, [dictData]);

  const suffixItemValue = Form.useWatch(props?.formKey) as string;

  // 获取其他医疗机构数据源
  const getOtherHospitalDataSource = async (searchKeyword) => {
    if (!searchKeyword) {
      return;
    }

    setLoading(true);
    const data = {
      Keyword: searchKeyword?.trim(),
    };

    try {
      const response: RespVO<{
        Data: OtherHospitalItem[];
        RecordsTotal: number;
      }> = await uniCommonService('Api/MetaData/Search/OtherHospital', {
        params: data,
      });

      if (response?.code === 0 && response?.statusCode === 200) {
        setSearchDataSource(
          response?.data?.Data?.filter((item) => item?.IsValid)?.map(
            (item) => ({
              ...item,
              key: item?.HospCode,
              value: item?.HospCode,
              label: `${item?.HospName || ''}`.trim(),
            }),
          ) || [],
        );
      }
    } catch (error) {
      console.error('获取其他医疗机构数据失败:', error);
      setSearchDataSource([]);
    }

    setLoading(false);
  };

  const { run: getOtherHospitalDataSourceWithDebounce } = useDebounceFn(
    (keyword) => {
      getOtherHospitalDataSource(keyword);
    },
    {
      wait: 200,
    },
  );

  const onSearchSelectClear = () => {
    form.setFieldValue(props?.formKey, '');
    // 清理状态
    setSearchDataSource([]);
    setHasSearched(false);
    setErrorTooltipOpen(false);
  };

  const onSearchSelect = (value: string) => {
    const currentSelected = searchDataSource?.find(
      (item) => item.value === value,
    );

    if (currentSelected) {
      // 保存选中的机构名称
      const selectedValue = currentSelected.HospName;
      form.setFieldValue(props?.formKey, selectedValue);

      // 重置搜索状态
      setErrorTooltipOpen(false);
      setHasSearched(false);
    }
  };

  return (
    <div
      style={props?.style || {}}
      className={`suffix-item-container ${props?.className || ''} ${
        props?.showInput || props?.showTags || props?.showSearch
          ? 'suffix-item-width-100'
          : ''
      }`}
    >
      <label className={'label'}>{props?.label}</label>
      {props?.showInput && (
        <Form.Item
          name={props?.formKey}
          className={'form-content-item-container'}
        >
          <Input
            id={props?.formItemId}
            className="suffix-input"
            bordered={false}
            value={suffixItemValue ?? ''}
            contentEditable={true}
            disabled={
              props?.disableCondition
                ? props?.disableCondition(conditionValue)
                : false
            }
            onChange={(event) => {
              form.setFieldValue(props?.formKey, event?.target?.value);
            }}
          />
        </Form.Item>
      )}
      {props?.showSearch && (
        <Form.Item
          name={props?.formKey}
          className={'form-content-item-container'}
        >
          <Tooltip
            open={errorTooltipOpen}
            color={'rgba(235, 87, 87, 0.85)'}
            title={'机构不存在，请检查后重新选择'}
          >
            <UniAntdSelect
              id={`${props?.formItemId}#Search`}
              className="suffix-input"
              style={{ width: '100%' }}
              showSearch
              showArrow={false}
              allowClear={false}
              disabled={
                props?.disableCondition
                  ? props?.disableCondition(conditionValue)
                  : false
              }
              dropdownMatchSelectWidth={false}
              getPopupContainer={() => getSelectorDropdownContainerNode()}
              onInputKeyDown={(event) => {
                if (hasSearched) {
                  if (event.key === 'Enter' && searchDataSource?.length === 0) {
                    setErrorTooltipOpen(true);
                    event.preventDefault();
                    event.stopPropagation();
                  }
                }
              }}
              enterSwitch={true}
              contentEditable={!inputFocusNotSelectAll}
              listHeight={tableSelectorDropdownHeight}
              placeholder={'请选择机构'}
              onSearch={(searchKeyword) => {
                setHasSearched(true);
                if (searchKeyword) {
                  getOtherHospitalDataSourceWithDebounce(searchKeyword);
                } else {
                  setSearchDataSource([]);
                }
              }}
              filterOption={false}
              notFoundContent={loading ? <Spin size="small" /> : null}
              onBlur={() => {
                setTimeout(() => {
                  setSearchDataSource([]);
                  setHasSearched(false);
                  setErrorTooltipOpen(false);
                }, 0);
              }}
              onClear={() => {
                onSearchSelectClear();
              }}
              onSelect={(value) => {
                onSearchSelect(value);
              }}
              value={suffixItemValue}
              options={searchDataSource}
              dumbOnComposition={true}
              mousedownOptionOpen={false}
              doubleClickPopUp={false}
            />
          </Tooltip>
        </Form.Item>
      )}
      {props?.showTags && (
        <div
          className={'suffix-item-tags-container form-content-item-container'}
        >
          <Form.Item name={props?.formKey} hidden={true} />
          <UniDmrSelect
            id={props?.formKey}
            formItemId={`${props?.formItemId}#DmrSelect`}
            showArrow={false}
            showAction={['click']}
            className="suffix-input"
            bordered={false}
            value={suffixItemValue?.replace('，', ',')?.split(',') ?? ''}
            placeholder={'请选择过敏药物'}
            mode={'multiple'}
            tokenSeparators={[',']}
            dataSource={selectDataSource}
            optionNameKey={'Name'}
            optionValueKey={'Name'}
            optionTitleKey={'Name'}
            maxTagCount={'responsive'}
            enablePinyinSearch={true}
            open={suffixSelectStatus}
            disabled={
              props?.disableCondition
                ? props?.disableCondition(conditionValue)
                : false
            }
            onDropdownVisibleChange={(open: boolean) => {
              setSuffixSelectStatus(open);
            }}
            onKeyDown={(event: any) => {
              if (event?.key === 'Enter' && suffixSelectStatus === true) {
                event?.stopPropagation();
              }

              if (event?.key === 'Esc' && suffixSelectStatus === true) {
                event?.preventDefault();
                event?.stopPropagation();
                setSuffixSelectStatus(false);
              }
            }}
            onChange={(event) => {
              if (Array.isArray(event)) {
                form.setFieldValue(
                  props?.formKey,
                  event?.filter((value) => !isEmptyValues(value)).join(','),
                );
              } else {
                form.setFieldValue(props?.formKey, event);
              }
            }}
          />
        </div>
      )}
    </div>
  );
};
