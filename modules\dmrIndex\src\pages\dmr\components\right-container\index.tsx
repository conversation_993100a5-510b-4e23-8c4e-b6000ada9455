import {
  Spin,
  Badge,
  Col,
  Collapse,
  List,
  Menu,
  Row,
  Tag,
  Tooltip,
  message,
  Radio,
  Switch,
  Avatar,
  Checkbox,
  Table,
  Tabs,
  Drawer,
  Empty,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import _, { isNumber } from 'lodash';
import './precheck.less';
import {
  ApartmentOutlined,
  CaretRightOutlined,
  AppstoreOutlined,
  BarsOutlined,
  DownOutlined,
  UpOutlined,
  ArrowRightOutlined,
  InfoCircleTwoTone,
} from '@ant-design/icons';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import {
  CardBundleCheck,
  CardBundleInfo,
  CenterSettleResult,
  DmrCardCheck,
  DmrChsGroup,
} from '@/pages/dmr/network/interfaces';
import assign from 'lodash/assign';
import pick from 'lodash/pick';
import keys from 'lodash/keys';
import { isEmptyValues } from '@uni/utils/src/utils';
import { useModel } from 'umi';
import cloneDeep from 'lodash/cloneDeep';
import { handleChsLabel } from '@uni/utils/src/cwUtils';
import chunk from 'lodash/chunk';
import { getZoomLevel } from '@uni/utils/src/ratio';
import {
  renderStandaloneCheckTags,
  StandaloneCheckCards,
} from '@/pages/dmr/components/right-container/standalone-check';

const { Panel } = Collapse;

const thirdPartyCheck =
  (window as any).externalConfig?.['dmr']?.['thirdPartyCheck'] ?? false;

const preCheckContainerPosition =
  (window as any).externalConfig?.['dmr']?.['preCheckContainerPosition'] ??
  'right';

export const errLevelDict = {
  '0': {
    title: '无错误',
    color: 'green',
    abbr: '无',
  },
  '1': {
    title: '弱提示性规则错误',
    color: 'cyan',
    abbr: '弱',
  },
  '2': {
    title: '提示性规则错误',
    color: 'blue',
    abbr: '提',
  },
  '3': {
    title: '审核非强规则错误',
    color: 'purple',
    abbr: '非强',
  },
  '4': {
    title: '一般规则错误',
    color: 'volcano',
    abbr: '普',
  },
  '5': {
    title: '审核强制规则错误',
    color: 'red',
    abbr: '强',
  },
};

export const drgProps = [
  {
    key: 'DrgCode',
    title: 'DRG编码',
  },
  // {
  //   key: 'ADrgName',
  //   title: '病种',
  // },
  {
    key: 'Rw',
    title: 'Rw',
  },
  {
    key: 'LowRisk',
    title: '是否低风险病种',
  },
  {
    key: 'AvgTotalFee',
    title: '标杆费用',
  },
  {
    key: 'AvgInPeriod',
    title: '标杆住院日',
  },
];
//drg组/rw/低风险死亡标识+三四级

export const HqmsThirdFourthOperRateMap = {
  '3': '三级',
  '4': '四级',
};

export const hqmsProps = [
  {
    key: 'DrgCode',
    title: 'DRG编码',
  },
  {
    key: 'DrgName',
    title: 'DRG名称',
  },
  // {
  //   key: 'ADrgName',
  //   title: '病种',
  // },
  {
    key: 'Rw',
    title: 'Rw',
  },
  {
    key: 'DrgDeadRisk',
    title: '低风险死亡标识',
  },
  {
    key: 'HqmsThirdFourthOperRate',
    title: '国考手术级别',
  },
];

const basicChsDrgProps = [
  {
    key: 'DrgCode',
    title: 'DRG编码',
  },
  {
    key: 'TotalFee',
    title: '总费用',
  },
  {
    key: 'CwValue',
    title: '支付标准',
  },
  {
    key: 'Profit',
    title: '预估盈亏',
  },
  {
    key: 'AvgFee',
    title: '标杆费用',
  },
  {
    key: 'AvgInPeriod',
    title: '标杆住院日',
  },
  {
    key: 'CwRate',
    title: '费率',
  },
  {
    key: 'Cw',
    title: 'Rw',
  },
  {
    key: 'BaseCwPoint',
    title: '基准点数',
  },
  {
    key: 'CwPointCoefficient',
    title: '成本系数',
  },
];

const basicChsDipProps = [
  {
    key: 'DrgCode',
    title: 'DRG编码',
  },
  {
    key: 'TotalFee',
    title: '总费用',
  },
  {
    key: 'CwValue',
    title: '支付标准',
  },
  {
    key: 'Profit',
    title: '预估盈亏',
  },
  {
    key: 'AvgFee',
    title: '标杆费用',
  },
  {
    key: 'AvgInPeriod',
    title: '标杆住院日',
  },
  {
    key: 'CwRate',
    title: '结算点值',
  },
  {
    key: 'Cw',
    title: '分值',
  },
  {
    key: 'BaseCwPoint',
    title: '基准点数',
  },
  {
    key: 'CwPointCoefficient',
    title: '成本系数',
  },
];

export const chsDrgDipProps = {
  Drg: [
    ...basicChsDrgProps,
    {
      key: 'AbnFeeType',
      title: '病例类型',
    },
  ],
  Dip: [
    ...basicChsDipProps,
    {
      key: 'AbnFeeType',
      title: '病例类型',
    },
  ],
};

export const adjustDrgDipProps = {
  Drg: [
    ...basicChsDrgProps,
    {
      key: 'ShuffleMessage',
      title: '调整建议',
    },
  ],
  Dip: [
    ...basicChsDipProps,
    {
      key: 'ShuffleMessage',
      title: '调整建议',
    },
  ],
};

export const chsSettleResultProps = [
  {
    key: 'ChsDrgCode',
    title: '医保分组编码',
  },
  {
    key: 'ChsDrgName',
    title: '医保分组名称',
  },
  {
    key: 'AbnFeeType',
    title: '病例类型',
  },
  {
    key: 'BaseCwPoint',
    title: '基准点数',
  },
  {
    key: 'CwPointCoefficient',
    title: '差异系数',
  },
  {
    key: 'CwPoint',
    title: '拨付点数',
  },
  {
    key: 'SetlId',
    title: '结算ID',
  },
];

interface PreCheckResultProps {
  hisId?: string;
  containerRef?: any;
  borderErrorInputs?: (errorKeys: string[], navigate?: boolean) => void;
  dmrProcessorInstance: any;
  preCheckModuleConfig: any;
  status?: boolean;
  setStatus?: (boolean: boolean) => void;
  // 是否是评审打开的AOD
  isExtraExamineCheck?: boolean;

  borderErrorInputsInTable?: (
    tableErrorKeyItem: any,
    navigate?: boolean,
  ) => void;

  enableStandaloneCheck?: boolean;
  standaloneCheckRef?: any;
}

const PreCheckResult = (props: PreCheckResultProps) => {
  const [currentMenuItemKey, setCurrentMenuItemKey] = useState('1');

  const [cardCheckData, setCardCheckData] = useState<any>({});

  const [cardGroupData, setCardGroupData] = useState<any>({});

  const [cardDrgGroupData, setCardDrgGroupData] = useState<any>({});

  const [cardHqmsGroupData, setCardHqmsGroupData] = useState<any>({});

  const [cardCalculated, setCardCalculated] = useState<any>(true);

  const [adjustDrgsResultData, setAdjustDrgsResultData] = useState<any>([]);

  const [cardSettleResultData, setCardSettleResultData] =
    useState<CenterSettleResult>({});

  const [drgMode, setDrgMode] = useState(true);
  const [chsMode, setChsMode] = useState(true);

  const [dmrChsGroupAuditData, setDmrChsGroupAuditData] = useState(undefined);

  const [needDmrChsGroupAudit, setNeedDmrChsGroupAudit] = useState(false);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  React.useImperativeHandle(props?.containerRef, () => {
    return {
      bundleCheck: (data) => {
        return dmrBundleCheckReq(
          data?.hisId,
          data?.originDmrCardInfo,
          data?.formFieldsValue,
        );
      },
    };
  });

  useEffect(() => {
    Emitter.on(EventConstant.DMR_CHSGROUP_AUDIT, (data) => {
      setDmrChsGroupAuditData(data);
      setNeedDmrChsGroupAudit(true);
    });

    Emitter.on(EventConstant.DMR_CARD_DATA_RESET, () => {
      setCardCheckData({});
      setCardGroupData({});
      setCardDrgGroupData({});
      setCardHqmsGroupData({});
      setAdjustDrgsResultData([]);
    });

    Emitter.on(EventConstant.DMR_CARD_SAVE_REVIEWS, (qualityCheckResult) => {
      if (isEmptyValues(qualityCheckResult)) {
        setCardCheckData({});
      } else {
        setCardCheckData({
          ...qualityCheckResult,
          Codes: _.orderBy(
            qualityCheckResult?.Reviews.filter(
              (item) => item.CheckCategory === '9',
            ),
            ['DisplayErrMsg'],
            ['asc'],
          ),
          Others: _.orderBy(
            qualityCheckResult?.Reviews.filter(
              (item) => item.CheckCategory !== '9',
            ),
            ['DisplayErrMsg'],
            ['asc'],
          ),
        });
      }
    });

    return () => {
      Emitter.off(EventConstant.DMR_CHSGROUP_AUDIT);
      Emitter.off(EventConstant.DMR_CARD_DATA_RESET);
      Emitter.off(EventConstant.DMR_CARD_SAVE_REVIEWS);
    };
  }, []);

  useEffect(() => {
    if (needDmrChsGroupAudit) {
      if (globalState?.dictData?.['Dmr'] && dmrChsGroupAuditData) {
        let data = cloneDeep(dmrChsGroupAuditData);
        dmrBundleCheckReq(
          data?.hisId,
          data?.originDmrCardInfo,
          data?.formFieldsValue,
        );

        if (thirdPartyCheck === true) {
          // 第三方 质控
          dmrBundleThirdPartyCheckReq(
            data?.hisId,
            data?.originDmrCardInfo,
            data?.formFieldsValue,
          );
        }

        setNeedDmrChsGroupAudit(false);
        setDmrChsGroupAuditData(undefined);
      }
    }
  }, [globalState, dmrChsGroupAuditData, needDmrChsGroupAudit]);

  // 接口 start
  const { loading: dmrBundleCheckLoading, run: dmrBundleCheckReq } = useRequest(
    async (hisId, originDmrCardInfo, formFieldsValue) => {
      let data: CardBundleInfo = Object.assign({}, originDmrCardInfo);

      let checkData =
        await props?.dmrProcessorInstance.cardSaveCheckParamProcessor(
          data,
          originDmrCardInfo,
          formFieldsValue,
          globalState?.dictData?.['Dmr'],
        );

      data = Object.assign({}, checkData);

      let reduced = new CardBundleInfo();
      let checkParams = assign(reduced, pick(data, keys(reduced)));
      checkParams['HisId'] = hisId;

      return uniCommonService('Api/Dmr/DmrCardBundle/Check', {
        method: 'POST',
        data: checkParams,
      });
    },
    {
      manual: true,
      formatResult: async (response: RespVO<CardBundleCheck>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          if (response?.data) {
            if (response?.data?.ChsMetricsResult) {
              let chsMetricsResult = response?.data?.ChsMetricsResult;
              if (chsMetricsResult?.ChsDrgsResult?.length) {
                setCardGroupData(chsMetricsResult.ChsDrgsResult[0]);
              } else {
                setCardGroupData({});
              }
              setCardCalculated(chsMetricsResult?.IsCalculated);
              if (chsMetricsResult?.AdjustDrgsResult?.length) {
                setAdjustDrgsResultData(chsMetricsResult?.AdjustDrgsResult);
              } else {
                setAdjustDrgsResultData([]);
              }
            } else {
              setCardGroupData({});
              setAdjustDrgsResultData([]);
            }

            if (response?.data?.DrgsMetricsResult) {
              setCardDrgGroupData(response?.data?.DrgsMetricsResult);
            } else {
              setCardDrgGroupData({});
            }

            if (response?.data?.HqmsMetricsResult) {
              setCardHqmsGroupData(response?.data?.HqmsMetricsResult);
            } else {
              setCardHqmsGroupData({});
            }

            if (response?.data?.QualityCheckResult) {
              let qualityCheckResult = response?.data?.QualityCheckResult;
              setCardCheckData({
                ...qualityCheckResult,
                Codes: _.orderBy(
                  qualityCheckResult?.Reviews.filter(
                    (item) => item.CheckCategory === '9',
                  ),
                  ['DisplayErrMsg'],
                  ['asc'],
                ),
                Others: _.orderBy(
                  qualityCheckResult?.Reviews.filter(
                    (item) => item.CheckCategory !== '9',
                  ),
                  ['DisplayErrMsg'],
                  ['asc'],
                ),
              });
            } else {
              setCardCheckData({});
            }

            if (response?.data?.CenterSettleResult) {
              setCardSettleResultData(response?.data?.CenterSettleResult);
            } else {
              setCardSettleResultData({});
            }

            // 国考3 4 级
            if (!isEmptyValues(response?.data?.HqmsMetricsResult)) {
              // UniqueId, OperType, OperCode
              response?.data?.HqmsMetricsResult?.OperDetails?.forEach(
                (item) => {
                  Emitter.emit(`BundleCheck-Extra-${item?.UniqueId}`, item);
                },
              );
            }
          } else {
            setCardGroupData({});
            setAdjustDrgsResultData([]);
            setCardDrgGroupData({});
            setCardHqmsGroupData({});
            setCardCheckData({});
          }
        } else {
          // TODO 提示信息
          message.error('接口错误');
        }

        return response ?? {};
      },
    },
  );

  const {
    loading: dmrBundleThirdPartyCheckLoading,
    run: dmrBundleThirdPartyCheckReq,
  } = useRequest(
    async (hisId, originDmrCardInfo, formFieldsValue) => {
      let data: CardBundleInfo = Object.assign({}, originDmrCardInfo);

      let checkData =
        await props?.dmrProcessorInstance.cardSaveCheckParamProcessor(
          data,
          originDmrCardInfo,
          formFieldsValue,
          globalState?.dictData?.['Dmr'],
        );

      data = Object.assign({}, checkData);

      let reduced = new CardBundleInfo();
      let checkParams = assign(reduced, pick(data, keys(reduced)));
      checkParams['HisId'] = hisId;

      return uniCommonService('Api/Dmr/DmrCardBundle/ThirdPartyCheck', {
        method: 'POST',
        data: checkParams,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        console.log('third party response', response);
        if (!isEmptyValues(response?.data)) {
          window.open(response?.data);
        } else {
          message.error('第三方质控结果不存在，请联系管理员');
        }
      },
    },
  );

  // 接口 end

  const actualColumnNameProcessor = (detailColumnsName: string) => {
    if (detailColumnsName?.startsWith('MainDiag')) {
      return {
        columnName: 'IcdeCode',
        columnIndex: 0,
      };
    }

    if (detailColumnsName?.startsWith('MainOper')) {
      return {
        columnName: 'OperCode',
        columnIndex: 0,
      };
    }

    let columnsNames = detailColumnsName?.split('|');
    let columnNameWithIndex = columnsNames?.at(0)?.split('~');
    return {
      columnName: columnNameWithIndex?.at(0),
      columnIndex: parseInt(columnNameWithIndex?.at(1)),
    };
  };

  const extraRuleCodeProcessor = (ruleCode: string) => {
    if (ruleCode?.toLowerCase()?.startsWith('9-0027-')) {
      return `diagnosisTable~IcdeCode~0`;
    }

    if (ruleCode?.startsWith('8-CUST-2014')) {
      return 'RedBloodCell';
    }

    return null;
  };

  const columnNameTableProcessor = (detailColumnName: string) => {
    let { columnName, columnIndex } =
      actualColumnNameProcessor(detailColumnName);

    // 年龄相关
    // 需要判定是不是有新版的AIO年龄来做跳转
    const ageKeys = ['PatAge', 'NwbAge', 'NwbAgeMonth', 'NwbAgeDay'];
    if (ageKeys?.includes(detailColumnName)) {
      let ageAio = document.querySelector('input[id*=PatAgeYMD]');
      if (!isEmptyValues(ageAio)) {
        return 'PatAge';
      }
    }

    // 病理相关字段 通过判定ColumnName 是不是Patho开头
    if (columnName?.toLowerCase()?.startsWith('patho')) {
      let pathologicalTable = document?.querySelectorAll(
        '#pathologicalDiagnosisTable',
      );

      if (!isEmptyValues(pathologicalTable)) {
        return `pathologicalDiagnosisTable~${columnName}~${columnIndex}`;
      } else {
        if (columnName === 'PathoDiagCode') {
          return 'IcdePathosItem';
        }

        if (columnName === 'PathoDiagName') {
          return 'IcdePathosIcdeName';
        }

        if (columnName === 'PalgNo') {
          return 'IcdePathosPalgNo';
        }
      }
    }

    // 动态判定 根据列来判定
    const diagnosisTableColumnElements = document?.querySelectorAll(
      '#diagnosisTable #tanstack-table-container thead th',
    );
    const diagnosisTableColumnIds = [].slice
      .call(diagnosisTableColumnElements)
      ?.map((elementItem) => {
        return elementItem.getAttribute('id')?.replace('th-', '');
      });

    const operationTableColumnElements = document?.querySelectorAll(
      '#operationTable #tanstack-table-container thead th',
    );
    const operationTableColumnIds = [].slice
      .call(operationTableColumnElements)
      ?.map((elementItem) => {
        return elementItem.getAttribute('id')?.replace('th-', '');
      });

    const icuTableColumnElements = document?.querySelectorAll(
      '#icuTable #tanstack-table-container thead th',
    );
    const icuTableColumnIds = [].slice
      .call(icuTableColumnElements)
      ?.map((elementItem) => {
        return elementItem.getAttribute('id')?.replace('th-', '');
      });

    if (diagnosisTableColumnIds.includes(columnName)) {
      return `diagnosisTable~${columnName}~${columnIndex}`;
    }

    if (operationTableColumnIds.includes(columnName)) {
      return `operationTable~${columnName}~${columnIndex}`;
    }

    if (icuTableColumnIds.includes(columnName)) {
      return `icuTable~${columnName}~${columnIndex}`;
    }

    return columnName;
  };

  const renderDrgResultCard = (props, data) => {
    return (
      <div>
        <Row gutter={[5, 5]}>
          {props.map((prop) => {
            switch (prop.key) {
              case 'DrgCode':
                return (
                  <Col span={24}>
                    <div className="title">
                      {data[prop.key]}&nbsp;&nbsp;
                      {data?.DrgName}
                    </div>
                  </Col>
                );
              case 'Cw':
              case 'CwRate':
                if (!data.PointBasedMethod) {
                  return (
                    <Col span={24} className="item">
                      <div className="name">{prop.title}</div>
                      <span className="value">{data[prop.key]}</span>
                    </Col>
                  );
                }
                break;
              case 'BaseCwPoint':
              case 'CwPointCoefficient':
                if (data.PointBasedMethod) {
                  return (
                    <Col span={24} className="item">
                      <div className="name">{prop.title}</div>
                      <span className="value">
                        {data[prop.key]?.toFixed(2)}
                      </span>
                    </Col>
                  );
                }
                break;
              case 'LowRisk':
                return (
                  <Col span={24} className="item">
                    <div className="name">{prop.title}</div>
                    <span className="value">
                      {data[prop.key]
                        ? '是'
                        : data[prop.key] === false
                        ? '否'
                        : ''}
                    </span>
                  </Col>
                );
              case 'ShuffleMessage':
                return (
                  <Col span={24} className="item">
                    <span className="value font-danger text-bold-600">
                      {data[prop.key]}
                    </span>
                  </Col>
                );
              default:
                return (
                  <Col span={24} className="item">
                    <div className="name">{prop.title}</div>
                    <span className="value">
                      {isNumber(data[prop.key])
                        ? data[prop.key].toFixed(2)
                        : data[prop.key]}
                    </span>
                  </Col>
                );
            }
          })}
        </Row>
      </div>
    );
  };

  const renderHqmsResultCard = (props, data) => {
    return (
      <div>
        <Row gutter={[5, 5]}>
          {props.map((prop) => {
            switch (prop.key) {
              case 'DrgCode':
              case 'DrgName':
                return (
                  <Col span={24} className="item">
                    <span className="name">{prop.title}</span>
                    <span
                      className="value"
                      style={{ wordBreak: 'break-all', maxWidth: '55%' }}
                    >
                      {data[prop?.key]}
                    </span>
                  </Col>
                );
              case 'DrgDeadRisk':
                return (
                  <Col span={24} className="item">
                    <div className="name">{prop.title}</div>
                    <span className="value">
                      {data[prop.key] === '0'
                        ? '低风险'
                        : data[prop.key] === '4'
                        ? '未知'
                        : '-'}
                    </span>
                  </Col>
                );
              case 'HqmsThirdFourthOperRate':
                return (
                  <Col span={24} className="item">
                    <div className="name">{prop.title}</div>
                    <span className="value">
                      {HqmsThirdFourthOperRateMap[data[prop.key]] ?? ''}
                    </span>
                  </Col>
                );
              default:
                return (
                  <Col span={24} className="item">
                    <div className="name">{prop.title}</div>
                    <span className="value">
                      {isNumber(data[prop.key])
                        ? data[prop.key].toFixed(2)
                        : data[prop.key]}
                    </span>
                  </Col>
                );
            }
          })}
        </Row>
      </div>
    );
  };

  const renderCenterSettleResult = (props, data) => {
    return (
      <div>
        <Row gutter={[5, 5]}>
          {props.map((prop) => {
            switch (prop.key) {
              case 'ChsDrgName':
              case 'ChsDrgCode':
              case 'AbnFeeType':
              case 'SetlId':
                return (
                  <Col span={24} className="item">
                    <span className="name">{prop.title}</span>
                    <span
                      className="value"
                      style={{ wordBreak: 'break-all', maxWidth: '55%' }}
                    >
                      {data[prop?.key]}
                    </span>
                  </Col>
                );
              case 'BaseCwPoint':
              case 'CwPointCoefficient':
              case 'CwPoint':
                return (
                  <Col span={24} className="item">
                    <div className="name">{prop.title}</div>
                    <span className="value">{data[prop.key]?.toFixed(2)}</span>
                  </Col>
                );
              default:
                return (
                  <Col span={24} className="item">
                    <div className="name">{prop.title}</div>
                    <span className="value">
                      {isNumber(data[prop.key])
                        ? data[prop.key].toFixed(2)
                        : data[prop.key]}
                    </span>
                  </Col>
                );
            }
          })}
        </Row>
      </div>
    );
  };

  const renderCheckCards = (data) => {
    if (data && data.length) {
      return _.map(data, (item, key, index) => {
        const errObj = errLevelDict[item?.ErrorLevel];
        // if (
        //   item?.Details.length === 1 &&
        //   _.isEqual(item?.DisplayErrMsg, item.Details[0]?.ErrMsg)
        // ) {
        //   let columnName = columnNameIcdeOperTableProcessor(
        //     item.Details[0]?.ColumnName,
        //   );

        //   return (
        //     <div className="check-content">
        //       {errObj && (
        //         <div className="d-flex" style={{ alignItems: 'center' }}>
        //           <div>
        //             <Tooltip title={errObj.title}>
        //               <Tag color={errObj.color}>{errObj.abbr}</Tag>
        //             </Tooltip>
        //           </div>
        //           <div>
        //             <a
        //               href={`#${encodeURIComponent(columnName)}`}
        //               onClick={(e) => {
        //                 e.preventDefault();
        //                 props.borderErrorInputs(
        //                   [`${encodeURIComponent(columnName)}`],
        //                   true,
        //                 );
        //               }}
        //             >
        //               {item.Details[0]?.ErrMsg}
        //             </a>
        //           </div>
        //         </div>
        //       )}
        //     </div>
        //   );
        // } else {
        return (
          <div className="check-content">
            <div className="d-flex" style={{ alignItems: 'center' }}>
              <div>
                {errObj && (
                  <Tooltip title={errObj.title}>
                    <Tag color={errObj.color}>{errObj.abbr}</Tag>
                  </Tooltip>
                )}
              </div>
              <div>{item?.SubType}</div>
            </div>
            {item.Details.map((det, i) => {
              let columnName =
                extraRuleCodeProcessor(det?.RuleCode) ??
                columnNameTableProcessor(det?.ColumnName);
              return (
                <div className="detail">
                  <a
                    href={`#${encodeURIComponent(columnName)}`}
                    onClick={(e) => {
                      e.preventDefault();
                      if (
                        !isEmptyValues(props?.borderErrorInputsInTable) &&
                        columnName?.includes('Table')
                      ) {
                        props.borderErrorInputsInTable(
                          {
                            key: columnName,
                            ...det,
                          },
                          true,
                        );
                      } else {
                        props.borderErrorInputs(
                          [`${encodeURIComponent(columnName)}`],
                          true,
                        );
                      }
                    }}
                  >
                    {det?.ErrMsg}
                  </a>
                </div>
              );
            })}
          </div>
        );
        // }
      });
    } else {
      return;
    }
  };

  const renderCheckCardsOnPositionBottom = (data) => {
    if (data && data.length) {
      return _.map(data, (item, key, index) => {
        const errObj = errLevelDict[item?.ErrorLevel];

        const detailsChunk = chunk(item.Details, 6);

        return (
          <div className="check-content">
            <div className="d-flex" style={{ alignItems: 'center' }}>
              <div>
                {errObj && (
                  <Tooltip title={errObj.title}>
                    <Tag color={errObj.color}>{errObj.abbr}</Tag>
                  </Tooltip>
                )}
              </div>
              <div>{item?.SubType}</div>
            </div>
            <div style={{ display: 'flex', flexDirection: 'row' }}>
              {detailsChunk?.map((detailsItem) => {
                return (
                  <div className={'chunk-container'}>
                    {detailsItem.map((det, i) => {
                      let columnName =
                        extraRuleCodeProcessor(det?.RuleCode) ??
                        columnNameTableProcessor(det?.ColumnName);
                      return (
                        <div className="detail">
                          <a
                            href={`#${encodeURIComponent(columnName)}`}
                            onClick={(e) => {
                              e.preventDefault();
                              if (
                                !isEmptyValues(
                                  props?.borderErrorInputsInTable,
                                ) &&
                                columnName?.includes('Table')
                              ) {
                                props.borderErrorInputsInTable(
                                  {
                                    key: columnName,
                                    ...det,
                                  },
                                  true,
                                );
                              } else {
                                props.borderErrorInputs(
                                  [`${encodeURIComponent(columnName)}`],
                                  true,
                                );
                              }
                            }}
                          >
                            {det?.ErrMsg}
                          </a>
                        </div>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        );
        // }
      });
    } else {
      return;
    }
  };

  const renderDrgResultCardOnBottom = (props, data) => {
    const specialKeys = ['ShuffleMessage', 'DrgCode'];

    const otherKeys = props?.filter(
      (item) => !specialKeys?.includes(item?.key),
    );

    const cardValueProcessor = (prop, data) => {
      switch (prop.key) {
        case 'Cw':
        case 'CwRate':
          if (!data.PointBasedMethod) {
            return (
              <div className="card-value-item">
                <div className="name">{prop.title}:</div>
                <span className="value">{data[prop.key]}</span>
              </div>
            );
          }
          break;
        case 'BaseCwPoint':
        case 'CwPointCoefficient':
          if (data.PointBasedMethod) {
            return (
              <div className="card-value-item">
                <div className="name">{prop.title}:</div>
                <span className="value">{data[prop.key]?.toFixed(2)}</span>
              </div>
            );
          }
          break;
        case 'LowRisk':
          return (
            <div className="card-value-item">
              <div className="name">{prop.title}:</div>
              <span className="value">
                {data[prop.key] ? '是' : data[prop.key] === false ? '否' : ''}
              </span>
            </div>
          );
        default:
          return (
            <div className="card-value-item">
              <div className="name">{prop.title}:</div>
              <span className="value">
                {isNumber(data[prop.key])
                  ? data[prop.key].toFixed(2)
                  : data[prop.key]}
              </span>
            </div>
          );
      }
    };

    return (
      <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
        <div className="title">
          <InfoCircleTwoTone />
          &nbsp;&nbsp;{data['DrgCode']}&nbsp;&nbsp;
          {data?.DrgName}
        </div>
        <div className={'other-key-chunk-container'}>
          <div className={'chunk-item'}>
            {otherKeys
              ?.filter((item, index) => index % 2 === 0)
              ?.map((prop) => {
                return cardValueProcessor(prop, data);
              })}
          </div>

          <div className={'chunk-item'} style={{ marginLeft: 40 }}>
            {otherKeys
              ?.filter((item, index) => index % 2 === 1)
              ?.map((prop) => {
                return cardValueProcessor(prop, data);
              })}
          </div>
        </div>

        <div className={'flex-row-center'}>
          <div className={'shuffle-left-container'} />
          <span className="value shuffle-message">
            {data['ShuffleMessage']}
          </span>
        </div>
      </div>
    );
  };

  const handleChsArr = (array) => {
    let cwTypes = {};
    try {
      cwTypes = JSON.parse(
        sessionStorage?.getItem('configurationInfo') ?? '{}',
      )?.chsDefs;
    } catch (e) {
      console.warn('handleChsArr JSON error', e);
    }
    return handleChsLabel(array, 'key', 'title', cwTypes);
  };

  const PreCheckBottomInfo = () => {
    const qualityCheckVisible =
      props?.preCheckModuleConfig?.cardReview !== false;
    const drgGroupVisible =
      props?.preCheckModuleConfig?.performancePreGroup !== false;
    const chsGroupVisible = props?.preCheckModuleConfig?.chsPreGroup !== false;

    const chsPreAdjustGroupVisible =
      props?.preCheckModuleConfig?.chsPreAdjustGroup !== false;

    const getQualityCheckTabs = () => {
      let qualityCheckTabs = [];

      if (props?.preCheckModuleConfig?.cardReviewDmrTrouble !== false) {
        qualityCheckTabs.push({
          key: 'DMR_WARNING',
          label: '首页问题',
          children: (
            <Spin spinning={dmrBundleCheckLoading} size={'small'}>
              <div
                id={'quality-check-warning-container'}
                className={'quality-check-warning-container'}
                onWheel={(event: any) => {
                  document
                    .getElementById('quality-check-warning-container')
                    ?.scrollBy({
                      left: event.deltaY < 0 ? -100 : 100,
                      behavior: 'smooth',
                    });
                }}
              >
                {isEmptyValues(cardCheckData.Others) ? (
                  <Empty
                    style={{ width: '100%' }}
                    description={'暂无首页问题'}
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                ) : (
                  <>{renderCheckCardsOnPositionBottom(cardCheckData.Others)}</>
                )}
              </div>
            </Spin>
          ),
        });
      }

      if (props?.preCheckModuleConfig?.cardReviewCodeTrouble !== false) {
        qualityCheckTabs.push({
          key: 'CODE_WARNING',
          label: '编码问题',
          children: (
            <Spin spinning={dmrBundleCheckLoading} size={'small'}>
              <div className={'quality-check-warning-container'}>
                {isEmptyValues(cardCheckData.Codes) ? (
                  <Empty
                    style={{ width: '100%' }}
                    description={'暂无编码问题'}
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                ) : (
                  <>{renderCheckCardsOnPositionBottom(cardCheckData.Codes)}</>
                )}
              </div>
            </Spin>
          ),
        });
      }

      return qualityCheckTabs;
    };

    return (
      <div className="pre-check-bottom-info-container">
        <Tabs tabPosition="left" size={'small'} className="pre-check-tabs">
          {qualityCheckVisible && (
            <Tabs.TabPane tab="质控审核" key="QUALITY_CHECK">
              <Tabs
                tabPosition="top"
                size={'small'}
                className="quality-check-tabs"
                items={getQualityCheckTabs()}
              />
            </Tabs.TabPane>
          )}

          {drgGroupVisible && (
            <Tabs.TabPane tab="绩效分组结果" key="DRG_GROUP">
              <Spin spinning={dmrBundleCheckLoading} size={'small'}>
                {Object.keys(cardDrgGroupData).length !== 0 ? (
                  <div className={'flex-row'}>
                    <div className="single-card">
                      {renderDrgResultCard(
                        handleChsArr(drgProps)?.filter(
                          (item) =>
                            props?.preCheckModuleConfig?.[
                              `DRG-${item?.key}`
                            ] !== false,
                        ),
                        cardDrgGroupData,
                      )}
                    </div>
                    {cardDrgGroupData?.Sds?.length > 0 && (
                      <div className={'single-disease-container-bottom'}>
                        <span className={'label'}>重点监控病种</span>
                        <div className={'sds-container'}>
                          {cardDrgGroupData?.Sds?.map((item) => {
                            return (
                              <span className={'sd-item'}>{item?.SdName}</span>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <Empty
                    style={{ width: '100%', height: '100%' }}
                    description={'暂无绩效分组结果'}
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </Spin>
            </Tabs.TabPane>
          )}

          {chsGroupVisible && (
            <Tabs.TabPane tab="医保预分组" key="CHS_GROUP">
              <Spin spinning={dmrBundleCheckLoading} size={'small'}>
                {cardCalculated ? (
                  <div className={'chs-group-container'}>
                    {!isEmptyValues(cardGroupData) && (
                      <div
                        className="single-card"
                        style={{ width: 300, marginLeft: 0 }}
                      >
                        {renderDrgResultCardOnBottom(
                          handleChsArr(
                            chsDrgDipProps[
                              cardGroupData?.SettleMethod || 'Drg'
                            ],
                          )?.filter(
                            (item) =>
                              props?.preCheckModuleConfig?.[
                                `CHS-${item?.key}`
                              ] !== false,
                          ),
                          cardGroupData,
                        )}
                      </div>
                    )}

                    {chsPreAdjustGroupVisible &&
                      adjustDrgsResultData.length !== 0 && (
                        <div className={'chs-group-adjust-separator'}>
                          <ArrowRightOutlined />
                          <span className={'adjust-label'}>可调整组</span>
                          <Badge
                            style={{ marginTop: 10, background: '#f4a741' }}
                            count={adjustDrgsResultData.length ?? 0}
                            overflowCount={99}
                          />
                        </div>
                      )}

                    {chsPreAdjustGroupVisible &&
                      adjustDrgsResultData.length !== 0 && (
                        <div className="multi-single-card">
                          {_.map(adjustDrgsResultData, (data) => {
                            return (
                              Object.keys(data).length !== 0 && (
                                <div className="single-card">
                                  {renderDrgResultCardOnBottom(
                                    adjustDrgDipProps[
                                      cardGroupData?.SettleMethod || 'Drg'
                                    ],
                                    data,
                                  )}
                                </div>
                              )
                            );
                          })}
                        </div>
                      )}
                  </div>
                ) : (
                  <span className={'not-calculate'}>
                    分组服务出现异常，请联系工程师
                  </span>
                )}
              </Spin>
            </Tabs.TabPane>
          )}
        </Tabs>
      </div>
    );
  };

  // 渲染默认的质控内容
  const renderDefaultPreCheckContent = () => (
    <div className={'dmr-precheck-container'}>
      <Collapse
        className="main-collapse"
        bordered={false}
        defaultActiveKey={['1', '2', '3', '4', 'HQMS_RIGHT_GROUP']}
        collapsible={'header'}
        expandIconPosition={'end'}
        style={{
          height: document.getElementById('dmr-container')?.offsetHeight,
          overflowY: 'auto',
        }}
      >
        <Panel
          header={'质控审核'}
          key="4"
          style={
            props?.preCheckModuleConfig?.cardReview !== false
              ? {}
              : { display: 'none' }
          }
        >
          <Spin spinning={dmrBundleCheckLoading} size={'small'}>
            <div className="inner-collapse-container">
              {/* <Checkbox onChange={()=>{} }>显示强制错误</Checkbox> */}
              <>
                <div
                  className="d-flex"
                  style={
                    props?.preCheckModuleConfig?.cardReviewDmrTrouble !== false
                      ? {
                          justifyContent: 'space-between',
                          fontSize: 15,
                          fontWeight: 'bold',
                          marginTop: 10,
                        }
                      : { display: 'none' }
                  }
                >
                  首页问题
                  <Badge count={cardCheckData?.Others?.length ?? 0} />
                </div>
                <>{renderCheckCards(cardCheckData?.Others)}</>
              </>
              <>
                <div
                  className="d-flex"
                  style={
                    props?.preCheckModuleConfig?.cardReviewCodeTrouble !== false
                      ? {
                          justifyContent: 'space-between',
                          fontSize: 15,
                          fontWeight: 'bold',
                          marginTop: 10,
                        }
                      : { display: 'none' }
                  }
                >
                  编码问题
                  <Badge count={cardCheckData?.Codes?.length ?? 0} />
                </div>
                <>{renderCheckCards(cardCheckData.Codes)}</>
              </>
            </div>
          </Spin>
        </Panel>
        <Panel
          header={<>绩效预分组</>}
          key="1"
          style={
            props?.preCheckModuleConfig?.performancePreGroup !== false
              ? {}
              : { display: 'none' }
          }
        >
          <Spin spinning={dmrBundleCheckLoading} size={'small'}>
            {Object.keys(cardDrgGroupData).length !== 0 && (
              <>
                <div className="single-card">
                  {renderDrgResultCard(
                    handleChsArr(drgProps)?.filter(
                      (item) =>
                        props?.preCheckModuleConfig?.[`DRG-${item?.key}`] !==
                        false,
                    ),
                    cardDrgGroupData,
                  )}
                </div>

                {cardDrgGroupData?.Sds?.length > 0 && (
                  <div className={'single-disease-container'}>
                    <span className={'label'}>重点监控病种</span>
                    <div className={'sds-container'}>
                      {cardDrgGroupData?.Sds?.map((item) => {
                        return (
                          <span className={'sd-item'}>{item?.SdName}</span>
                        );
                      })}
                    </div>
                  </div>
                )}
              </>
            )}
          </Spin>
        </Panel>
        <Panel
          header="医保预分组"
          key="2"
          style={
            props?.preCheckModuleConfig?.chsPreGroup !== false
              ? {}
              : { display: 'none' }
          }
        >
          <Spin spinning={dmrBundleCheckLoading} size={'small'}>
            {cardCalculated ? (
              <div className="inner-collapse-container">
                <div style={{ borderBottom: '1px solid #e9e9e9' }}>
                  <div className="single-card">
                    {renderDrgResultCard(
                      handleChsArr(
                        chsDrgDipProps[cardGroupData?.SettleMethod || 'Drg'],
                      )?.filter(
                        (item) =>
                          props?.preCheckModuleConfig?.[`CHS-${item?.key}`] !==
                          false,
                      ),
                      cardGroupData,
                    )}
                  </div>
                </div>
                <Collapse
                  style={{ width: '100%' }}
                  defaultActiveKey={[]}
                  bordered={false}
                >
                  <Panel
                    key="2"
                    header={
                      <div
                        className="d-flex"
                        style={{ justifyContent: 'space-between' }}
                      >
                        可调整组
                        {adjustDrgsResultData.length !== 0 && (
                          <Badge
                            color="#f4a741"
                            count={adjustDrgsResultData.length}
                          />
                        )}
                      </div>
                    }
                    style={
                      props?.preCheckModuleConfig?.chsPreAdjustGroup !== false
                        ? {}
                        : { display: 'none' }
                    }
                  >
                    <div className="multi-single-card">
                      {adjustDrgsResultData.length !== 0 &&
                        _.map(adjustDrgsResultData, (data) => {
                          return (
                            Object.keys(data).length !== 0 && (
                              <div className="single-card">
                                {renderDrgResultCard(
                                  adjustDrgDipProps[
                                    cardGroupData?.SettleMethod || 'Drg'
                                  ],
                                  data,
                                )}
                              </div>
                            )
                          );
                        })}
                    </div>
                  </Panel>
                  <Panel
                    key="3"
                    header={'分项费用标杆值'}
                    style={
                      props?.preCheckModuleConfig?.chsFeeTypeFeesTable !== false
                        ? {}
                        : { display: 'none' }
                    }
                  >
                    <Row>
                      <Col span={24}>
                        <Table
                          className="dmr-bm-fee-table"
                          columns={[
                            {
                              title: '费用类型',
                              dataIndex: 'FeeType',
                            },
                            {
                              title: '当前费用',
                              dataIndex: 'Fee',
                              render: (_) => <>{_ ? _?.toFixed(2) : _}</>,
                            },
                            {
                              title: '标杆费用',
                              dataIndex: 'AvgFee',
                              render: (_) => <>{_ ? _?.toFixed(2) : _}</>,
                            },
                          ]}
                          dataSource={cardGroupData?.FeeTypeFees}
                          size="small"
                          pagination={false}
                        />
                      </Col>
                    </Row>
                  </Panel>
                </Collapse>
              </div>
            ) : (
              <span className={'not-calculate'}>
                分组服务出现异常，请联系工程师
              </span>
            )}
          </Spin>
        </Panel>
        <Panel
          header={<>国考预分组</>}
          key="HQMS_RIGHT_GROUP"
          style={
            props?.preCheckModuleConfig?.hqmsPreGroup !== false
              ? {}
              : { display: 'none' }
          }
        >
          <Spin spinning={dmrBundleCheckLoading} size={'small'}>
            {Object.keys(cardHqmsGroupData).length !== 0 && (
              <>
                <div className="single-card">
                  {renderHqmsResultCard(
                    handleChsArr(hqmsProps)?.filter(
                      (item) =>
                        props?.preCheckModuleConfig?.[`HQMS-${item?.key}`] !==
                        false,
                    ),
                    cardHqmsGroupData,
                  )}
                </div>
              </>
            )}
          </Spin>
        </Panel>
        <Panel
          key="3"
          header={'医保反馈结果'}
          style={
            props?.preCheckModuleConfig?.chsSettleResult !== false
              ? {}
              : { display: 'none' }
          }
        >
          <Spin spinning={dmrBundleCheckLoading} size={'small'}>
            {
              <div className="multi-single-card">
                {
                  <div className="single-card">
                    {renderCenterSettleResult(
                      chsSettleResultProps?.filter(
                        (item) =>
                          props?.preCheckModuleConfig?.[
                            `CHS-SETTLE-${item?.key}`
                          ] !== false,
                      ),
                      cardSettleResultData,
                    )}
                  </div>
                }
              </div>
            }
          </Spin>
        </Panel>
        <div style={{ height: 200, background: 'transparent' }} />
      </Collapse>
    </div>
  );

  // 如果isExtraExamineCheck为true，优先返回默认DOM内容 优先级最高
  if (props?.isExtraExamineCheck === true) {
    return renderDefaultPreCheckContent();
  }

  if (preCheckContainerPosition === 'bottom') {
    return (
      <div className={'dmr-precheck-container'}>
        <Drawer
          placement="bottom"
          mask={true}
          maskClosable={true}
          height={300}
          className={'pre-check-bottom-container'}
          open={props?.status}
          onClose={() => {
            props?.setStatus && props?.setStatus(false);
          }}
          title={``}
          getContainer={false}
        >
          <div
            className={'close-trigger-container'}
            onClick={() => {
              props?.setStatus && props?.setStatus(false);
            }}
          >
            <DownOutlined style={{ color: '#ffffff' }} />
          </div>

          <PreCheckBottomInfo />
        </Drawer>
      </div>
    );
  }

  if (props?.enableStandaloneCheck === true) {
    return (
      <div
        className={'dmr-standalone-container standalone-check-card-container'}
        style={{
          height: document.getElementById('dmr-root-container')?.offsetHeight,
          overflowY: 'auto',
        }}
      >
        <StandaloneCheckCards
          hisId={props?.hisId}
          data={cardCheckData?.Others?.concat(cardCheckData?.Codes)}
          errLevelDict={errLevelDict}
          columnNameTableProcessor={columnNameTableProcessor}
          borderErrorInputs={props?.borderErrorInputs}
          standaloneCheckRef={props?.standaloneCheckRef}
        />
      </div>
    );
  }

  // 原有的其他逻辑保持不变，最后返回默认内容
  return renderDefaultPreCheckContent();
};

export default PreCheckResult;
