import React, { useEffect, useState, useMemo, ReactNode } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import {
  <PERSON>ton,
  Card,
  Col,
  Row,
  Input,
  Spin,
  Collapse,
  List,
  TableProps,
  Badge,
  Tag,
  message,
  Tree,
  Space,
  Divider,
  Tooltip,
  ColProps,
} from 'antd';
import _ from 'lodash';
import UniTable, { UniTableProps } from '../table';
import ExportIconBtn, { IExportIconBtnProps } from '../backend-export';
import { DownOutlined } from '@ant-design/icons';
import { DataNode } from 'antd/lib/tree';
import {
  TableColumnEditButton,
  TableColumnEditButtonProps,
} from '../table/column-edit';
import clsx from 'clsx';
import './index.less';
import { pinyinInitialSearch } from '@uni/utils/src/pinyin';

const { Search } = Input;

export interface ITreeCtrlTableProps {
  treeData: {
    title?: string;
    subTitle?: string;
    subKey?: string;
    data: {
      treeDataResult: DataNode[];
      [propName: string]: number | DataNode[]; // 一个number
    };
    noTag?: boolean;
  };
  treeLoading?: boolean;
  treeAction: {
    onSearch?: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onSelect: (selectKeys: string[], info) => void;
  };
  className?: string;
  tableData: UniTableProps<any> & {
    tableTitle?: string | ReactNode;
    tableTitleExtra?: string | ReactNode;
    export?: IExportIconBtnProps;
    columnEdit?: TableColumnEditButtonProps;
  };
  treeColProps?: ColProps;
  tableColProps?: ColProps;
  isAsync?: boolean;
  onLoadData?: (node: any) => Promise<void>;
  defaultExpandedKeys?: any[];
}

interface SearchResult {
  data: DataNode[];
  expandedKeys: React.Key[];
}

// 处理搜索展开和高亮
export const getTreeFilterWithSearchedText = (
  searchedText: string,
  arr: DataNode[],
): SearchResult => {
  // Return original data if search text is empty
  if (!searchedText?.trim()) {
    return {
      data: arr,
      expandedKeys: arr.length > 0 ? [arr[0].key] : [],
    };
  }

  const res: DataNode[] = [];
  const expandedKeys = new Set<React.Key>();

  const isMatch = (title: string, search: string) => {
    const searchTrim = search.trim();
    if (!searchTrim) return false;

    const titleLower = title.toLowerCase();
    const searchLower = searchTrim.toLowerCase();

    const normalMatch = titleLower.includes(searchLower);
    const pinyinMatch = pinyinInitialSearch(titleLower, searchLower);
    return normalMatch || pinyinMatch;
  };

  const processNode = (
    node: DataNode,
    parentKey?: React.Key,
  ): DataNode | null => {
    const children = node.children
      ? node.children
          .map((child) => processNode(child, node.key))
          .filter((n): n is DataNode => n !== null)
      : [];

    const nodeTitle = typeof node.title === 'string' ? node.title : '';
    const match = isMatch(nodeTitle, searchedText);

    if (children.length || match) {
      if (match && parentKey) {
        expandedKeys.add(parentKey);
      }
      if (children.length) {
        expandedKeys.add(node.key);
      }

      const titleElement = searchedText ? (
        <span>
          {nodeTitle
            .split(new RegExp(`(${searchedText})`, 'i'))
            .map((fragment, i) =>
              isMatch(fragment, searchedText) ? (
                <span key={i} style={{ color: '#f50' }}>
                  {fragment}
                </span>
              ) : (
                fragment
              ),
            )}
        </span>
      ) : (
        nodeTitle
      );

      return {
        ...node,
        title: titleElement,
        children: children.length > 0 ? children : undefined,
      };
    }

    return null;
  };

  arr.forEach((node) => {
    const processed = processNode(node);
    if (processed) {
      res.push(processed);
    }
  });

  return {
    data: res,
    expandedKeys: Array.from(expandedKeys),
  };
};

const getParentKey = (key: React.Key, tree: DataNode[]): React.Key => {
  let parentKey: React.Key;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some((item) => item.key === key)) {
        parentKey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey!;
};

const TreeCtrlTable = ({
  treeData,
  treeLoading,
  treeAction,
  className,
  tableData,
  treeColProps,
  tableColProps,
  isAsync = false,
  defaultExpandedKeys = [],
  onLoadData,
}: ITreeCtrlTableProps) => {
  // tree state
  const [searchValue, setSearchValue] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [treeRenderData, setTreeRenderData] = useState<DataNode[]>([]);
  const [loadedKeys, setLoadedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  const isEmptySearch = (value: any): boolean => {
    return value === undefined || value === null || value === '';
  };

  const defaultSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchText = e?.target.value;
    setSearchValue(searchText);

    if (isEmptySearch(searchText)) {
      setTreeRenderData(treeData?.data?.treeDataResult || []);
      setExpandedKeys(
        [treeData?.data?.treeDataResult?.at(0)?.key as React.Key].concat(
          defaultExpandedKeys,
        ),
      );
      setAutoExpandParent(false);
      return;
    }

    const { data: searchedData, expandedKeys: newExpandedKeys } =
      getTreeFilterWithSearchedText(
        searchText,
        treeData?.data?.treeDataResult || [],
      );

    setTreeRenderData(searchedData);
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(true);
  };

  const defaultExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  useEffect(() => {
    if (treeData?.data?.treeDataResult?.length > 0) {
      setTreeRenderData(treeData?.data?.treeDataResult);
      setExpandedKeys(
        [treeData?.data?.treeDataResult?.at(0)?.key as string].concat(
          defaultExpandedKeys,
        ),
      );
    }
  }, [treeData?.data?.treeDataResult]);

  console.log('treeData', tableData);

  const handleLoadData = async (node: any) => {
    if (!isAsync || !onLoadData) return;

    try {
      await onLoadData(node);
      setLoadedKeys((prev) => [...prev, node.key as string]);
    } catch (error) {
      console.error('Failed to load children:', error);
    }
  };

  return (
    <Card className={clsx('tree_ctrl_table_container', className)}>
      <Row gutter={20} wrap={false}>
        <Col
          {...(treeColProps ? { ...treeColProps } : { span: 6 })}
          className="border-right"
        >
          <div
            className="d-flex"
            style={{ justifyContent: 'space-between', padding: '12px 0px' }}
          >
            <div className="title">{treeData?.title}</div>
            <div>
              <Tag color="red" style={{ marginRight: 0 }}>
                {treeData?.subTitle || '问题病例数'}：
                {(treeData?.data?.[
                  treeData?.subKey ??
                    Object.keys(treeData?.data)?.find((d) =>
                      _.isNumber(treeData?.data?.[d]),
                    )
                ] as number) || '0'}
              </Tag>
            </div>
          </div>
          <Spin spinning={treeLoading}>
            <div className="left-container">
              <Search
                style={{ padding: '0 0px 8px' }}
                placeholder="搜索关键字"
                onChange={(e) => {
                  if (treeAction?.onSearch) {
                    treeAction?.onSearch(e);
                    // defaultSearch(e);
                    return;
                  }
                  defaultSearch(e);
                }}
              />
              <Tree
                showLine
                switcherIcon={
                  <DownOutlined
                    onPointerEnterCapture={undefined}
                    onPointerLeaveCapture={undefined}
                  />
                }
                blockNode
                defaultExpandedKeys={defaultExpandedKeys || []}
                onExpand={defaultExpand}
                expandedKeys={expandedKeys}
                autoExpandParent={autoExpandParent}
                treeData={treeRenderData}
                onSelect={treeAction.onSelect}
                loadData={isAsync ? handleLoadData : undefined}
                loadedKeys={loadedKeys}
                titleRender={(nodeData: any) => {
                  return (
                    <>
                      <Tooltip title={nodeData?.remark}>
                        <div>
                          {nodeData?.Sort > -1 ? `${nodeData?.Sort}.` : ''}{' '}
                          {nodeData?.title}
                        </div>
                      </Tooltip>
                      {!treeData?.noTag && (
                        <div>
                          <Tag>{nodeData?.value}</Tag>
                        </div>
                      )}
                    </>
                  );
                }}
                style={{
                  height:
                    document.getElementById('site-layout-content')
                      ?.offsetHeight -
                    50 -
                    30 -
                    // - document.getElementById('details-type-form')?.offsetHeight
                    // - 70
                    30 -
                    50 -
                    50 -
                    24,
                  overflowY: 'auto',
                }}
              />
            </div>
          </Spin>
        </Col>
        <Col {...(tableColProps ? { ...tableColProps } : { span: 18 })}>
          <UniTable
            scroll={{ x: 'max-content' }}
            title={() => (
              <div
                className="table-header d-flex"
                style={{ justifyContent: 'space-between' }}
              >
                <span className="title">{tableData?.tableTitle}</span>
                <Space>
                  {tableData?.tableTitleExtra}
                  <Divider type="vertical" />
                  {tableData?.export && (
                    <ExportIconBtn {...tableData?.export} />
                  )}
                  {tableData?.columnEdit && (
                    <TableColumnEditButton {...tableData?.columnEdit} />
                  )}
                </Space>
              </div>
            )}
            {...tableData}
            showSorterTooltip={{ placement: 'bottom', title: '排序' }}
          />
        </Col>
      </Row>
    </Card>
  );
};

export default TreeCtrlTable;
