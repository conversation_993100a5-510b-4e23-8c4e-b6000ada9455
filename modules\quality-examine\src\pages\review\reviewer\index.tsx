import { <PERSON><PERSON>, Card, Col, Form, message, Modal, Row } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import {
  ReviewHeaderErrorProgress,
  reviewHeaderErrorProgressItems,
  ReviewHeaderErrorTotals,
  ReviewHeaderGauge,
  ReviewHeaderProgress,
  ReviewHeaderTotals,
} from '@/pages/review/components/header';
import React, { useEffect, useRef, useState } from 'react';
import './index.less';
import { useRequest } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { BatchItem, TaskStatusSummary } from '@/pages/review/interface';
import { useUpdateEffect } from 'ahooks';
import { isEmptyValues } from '@uni/utils/src/utils';
import { dmrReviewColumns } from '@/pages/review/columns';
import ScoreCommentDrawerContainer from '@/pages/review/components/score-comment';
import { QualityExamineStatus } from '@/pages/review/components/score-comment/score/constant';
import { ReviewTaskCreateContainerSimple } from '@/pages/review/components/review-task/task-create-simple';
import ReviewTable, {
  getUserEmployeeCode,
} from '@/pages/review/components/review-table';
import ReviewPeriodSelect from '@/pages/review/components/period-select';
import { examineSummarySpan, sortByBatchDate } from '@/pages/review/utils';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { useModel } from 'umi';
import { useRouteProps } from '@uni/commons/src/route-context';
import ExportIconBtn from '@uni/components/src/backend-export';
import Stats from '../components/stats';

const examineType =
  (window as any).externalConfig?.['dmr']?.['examineType'] ?? 'Score';

const examineSummaryShow =
  (window as any).externalConfig?.['qualityExamine']?.['examineSummaryShow'] ??
  true;

const DmrReviewReviewer = () => {
  const [form] = Form.useForm();
  // const taskCreateContainerRef = useRef(null);

  const taskTableRef = useRef(null);
  const dmrPreviewContainerRef = useRef(null);
  const dmrReviewerContainerRef = useRef(null);

  const [searchParams, setSearchParams] = useState<any>({});
  const [selectedStatItem, setSelectedStatItem] = useState<string>('');

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const { examineMasterId } = useRouteProps();

  const [taskSummaryInfo, setTaskSummaryInfo] =
    useState<TaskStatusSummary>(undefined);

  React.useImperativeHandle(dmrReviewerContainerRef, () => {
    return {
      cancelReview: (record: any, index: number) => {
        cancelReview(record);
      },
      instantReview: (record: any, index: number) => {
        instantReview(record);
      },
    };
  });

  useEffect(() => {
    console.log('examineMasterId', examineMasterId);
    latestBatchInfoReq();
    // if (isEmptyValues(batchId)) {
    //   if (isEmptyValues(globalState?.searchParams?.BatchItem)) {
    //     latestBatchInfoReq();
    //   } else {
    //     if (isEmptyValues(examineMasterId)) {
    //       setBatchId(globalState?.searchParams?.BatchItem?.BatchId);
    //       setBatchInfo(globalState?.searchParams?.BatchItem);
    //     } else {
    //       if (
    //         globalState?.searchParams?.BatchItem?.MasterId === examineMasterId
    //       ) {
    //         setBatchId(globalState?.searchParams?.BatchItem?.BatchId);
    //         setBatchInfo(globalState?.searchParams?.BatchItem);
    //       } else {
    //         latestBatchInfoReq();
    //       }
    //     }
    //   }
    // }
  }, [examineMasterId]);

  useUpdateEffect(() => {
    taskStatusSummaryReq();
    taskTableRef?.current?.freshQueryTable();
  }, [searchParams]);

  useEffect(() => {
    latestBatchInfoReq();
  }, []);

  const { loading: latestBatchInfoLoading, run: latestBatchInfoReq } =
    useRequest(
      () => {
        let data = {};
        if (!isEmptyValues(examineMasterId)) {
          data['MasterId'] = examineMasterId;
        }

        return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetBatches', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<BatchItem[]>) => {
          let latestBatch = response?.data?.sort(sortByBatchDate)?.at(0);
          let defaultBatchItem = globalState?.searchParams?.BatchItem;
          const findBatchItem = response?.data?.find(
            (item) =>
              item.BatchId === defaultBatchItem?.BatchId &&
              item.MasterId === defaultBatchItem?.MasterId,
          );
          if (findBatchItem) {
            setSearchParams({
              ...searchParams,
              BatchId: defaultBatchItem.BatchId,
              MasterId: defaultBatchItem?.MasterId,
              BatchItem: globalState?.searchParams?.BatchItem,
              batchInfo: globalState?.searchParams?.BatchItem,
            });
          } else {
            let batchInfo = latestBatch;
            if (!isEmptyValues(batchInfo?.BatchArgs)) {
              try {
                batchInfo['BatchArgsItem'] = JSON.parse(batchInfo?.BatchArgs);
              } catch (e) {
                batchInfo['BatchArgsItem'] = undefined;
              }
            }
            setSearchParams({
              ...searchParams,
              BatchId: latestBatch?.BatchId,
              MasterId: latestBatch?.MasterId,
              BatchItem: batchInfo,
              batchInfo: batchInfo,
            });
          }
        },
      },
    );

  const { loading: taskStatusSummaryLoading, run: taskStatusSummaryReq } =
    useRequest(
      () => {
        return uniCommonService(
          'Api/Dmr/DmrCardQualityExamine/GetTaskStatusSummary',
          {
            method: 'POST',
            data: {
              reviewerCodes: getReviewerCodes(),
              ...searchParams,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<TaskStatusSummary>) => {
          let summaryInfo = response?.data;
          summaryInfo['CommentErrorTotal'] =
            reviewHeaderErrorProgressItems?.reduce(
              (accumulator, item) =>
                accumulator + (summaryInfo?.[item?.valueKey] ?? 0),
              0,
            );
          setTaskSummaryInfo(summaryInfo ?? {});
        },
      },
    );

  const cancelReview = async (taskItem: any) => {
    if (taskItem?.Status === QualityExamineStatus.Pending) {
      message.warning('待审核状态不能被重置');
      return;
    }

    if (taskItem?.Status === QualityExamineStatus.Reviewing) {
      message.warning('审核中状态不能被重置');
      return;
    }

    if (taskItem?.Status === QualityExamineStatus.Init) {
      message.warning('状态错误，请检查');
      return;
    }

    let cancelReviewResponse: RespVO<any> = await uniCommonService(
      'Api/Dmr/DmrCardQualityExamine/ResetTask',
      {
        method: 'POST',
        data: {
          TaskId: taskItem?.TaskId,
        },
      },
    );

    if (
      cancelReviewResponse?.code === 0 &&
      cancelReviewResponse?.statusCode === 200
    ) {
      taskStatusSummaryReq();
      taskTableRef?.current?.freshQueryTable();
    }
  };

  const instantReview = async (taskItem: any) => {
    if (
      [
        QualityExamineStatus.Pending,
        QualityExamineStatus.Reviewing,
        // QualityExamineStatus.Reviewed,
        // QualityExamineStatus.Rejected,
        // QualityExamineStatus.ReSubmitted,
      ]?.includes(taskItem?.Status)
    ) {
      let cancelReviewResponse: RespVO<any> = await uniCommonService(
        'Api/Dmr/DmrCardQualityExamine/AcceptTask',
        {
          method: 'POST',
          data: {
            TaskId: taskItem?.TaskId,
          },
        },
      );

      if (
        cancelReviewResponse?.code === 0 &&
        cancelReviewResponse?.statusCode === 200
      ) {
        taskStatusSummaryReq();
        taskTableRef?.current?.freshQueryTable();
      }
    } else {
      message.warning('状态错误，请检查状态');
    }
  };

  // 一键提交
  const { loading: taskAcceptAllLoading, run: taskAcceptAllReq } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrCardQualityExamine/AcceptTaskAll', {
        method: 'POST',
        data: {
          reviewerCodes: getReviewerCodes(),
          ...searchParams,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        // 刷新一下列表
        taskStatusSummaryReq();
        taskTableRef?.current?.freshQueryTable();
      },
    },
  );

  // const onTaskDropdownClick = ({ item, key }) => {
  //   let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
  //   if ((userInfo?.Roles ?? [])?.includes('Admin')) {
  //     if (isEmptyValues(searchParams?.adminReviewerCode)) {
  //       message.error('请先选择编码员');
  //       return;
  //     }
  //   }

  //   let maxRandomValue =
  //     (taskSummaryInfo?.TargetTaskCnt ?? 0) - (taskSummaryInfo?.TaskCnt ?? 0);

  //   if (key === 'NUMBER_RANDOM') {
  //     if (maxRandomValue <= 0) {
  //       message.success('评审数量已达到目标数量');
  //       return;
  //     }
  //   }

  //   taskCreateContainerRef?.current?.setReviewCreateStatus({
  //     status: true,
  //     type: key,
  //     maxRandomValue: maxRandomValue,
  //   });
  // };

  const getReviewerCodes = () => {
    let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
    if ((userInfo?.Roles ?? [])?.includes('Admin')) {
      if (isEmptyValues(searchParams?.adminReviewerCode)) {
        return [];
      } else {
        return searchParams?.adminReviewerCode;
      }
    }

    return [userInfo?.EmployeeCode];
  };

  // const getReviewerCode = () => {
  //   let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
  //   if ((userInfo?.Roles ?? [])?.includes('Admin')) {
  //     if (isEmptyValues(searchParams?.adminReviewerCode)) {
  //       return undefined;
  //     } else {
  //       return searchParams?.adminReviewerCode;
  //     }
  //   }

  //   return userInfo?.EmployeeCode;
  // };

  return (
    <div id={'reviewer-container'} className={'reviewer-container'}>
      <ReviewPeriodSelect
        form={form}
        searchParams={searchParams}
        setSearchParams={setSearchParams}
        onBatchDeleteSuccess={() => {
          // 刷一下
          latestBatchInfoReq();
        }}
        currentUserEmployeeCode={getUserEmployeeCode()}
      />

      {examineSummaryShow === true && (
        <Row gutter={[8, 8]} className={'stats-container'}>
          <Col span={examineSummarySpan?.[examineType]?.at(0)}>
            <ReviewHeaderTotals
              needTarget={true}
              summaryInfo={taskSummaryInfo}
              loading={taskStatusSummaryLoading || latestBatchInfoLoading}
            />
          </Col>
          <Col span={examineSummarySpan?.[examineType]?.at(1)}>
            <ReviewHeaderProgress
              summaryInfo={taskSummaryInfo}
              loading={taskStatusSummaryLoading || latestBatchInfoLoading}
            />
          </Col>
          <Col span={examineSummarySpan?.[examineType]?.at(2)}>
            <ReviewHeaderErrorTotals
              summaryInfo={taskSummaryInfo}
              loading={taskStatusSummaryLoading || latestBatchInfoLoading}
            />
          </Col>
          {examineType === 'Score' && (
            <Col span={examineSummarySpan?.[examineType]?.at(3)}>
              <ReviewHeaderGauge
                summaryInfo={taskSummaryInfo}
                loading={taskStatusSummaryLoading || latestBatchInfoLoading}
              />
            </Col>
          )}

          {examineType === 'Comment' && (
            <Col span={examineSummarySpan?.[examineType]?.at(3)}>
              <ReviewHeaderErrorProgress
                summaryInfo={taskSummaryInfo}
                loading={taskStatusSummaryLoading || latestBatchInfoLoading}
              />
            </Col>
          )}
        </Row>
      )}
      <div className="review-person-summary-container">
        <Stats
          selectedStatItem={selectedStatItem}
          selectOption="PendingTaskCnt"
          setSelectedStatItem={setSelectedStatItem}
          summaryInfo={taskSummaryInfo}
          loading={taskStatusSummaryLoading}
        />
      </div>
      <Card
        style={{ marginTop: 8 }}
        title={'评审病案'}
        extra={
          <>
            {/*一键提交所有病案*/}
            <Button
              type={'default'}
              style={{ marginRight: 10 }}
              disabled={
                isEmptyValues(taskSummaryInfo?.[selectedStatItem]) ||
                taskSummaryInfo?.[selectedStatItem] === 0
              }
              onClick={() => {
                Modal.confirm({
                  title: `确认批量提交 ${
                    taskSummaryInfo?.[selectedStatItem] || 0
                  } 份病案吗？`,
                  icon: <ExclamationCircleOutlined />,
                  onOk() {
                    taskAcceptAllReq();
                  },
                  onCancel() {},
                });
              }}
            >
              一键提交
            </Button>

            <ExportIconBtn
              btnText={'导出'}
              btnType={'default'}
              getExternalExportConfig={() => {
                return {
                  isBackend: true,
                  backendObj: {
                    url: 'Api/Dmr/DmrCardQualityExamine/ExportGetTasks',
                    method: 'POST',
                    data: {
                      reviewerCodes: getReviewerCodes(),
                      ...taskTableRef?.current?.getFastSelectKeyToStatuses(),
                      ...searchParams,
                      ...(selectedStatItem
                        ? { [`${selectedStatItem}Flag`]: true }
                        : {}),
                    },
                    fileName: '待审病案',
                  },
                };
              }}
            />

            <TableColumnEditButton
              columnInterfaceUrl={'Api/Dmr/DmrCardQualityExamine/GetTasks'}
              onTableRowSaveSuccess={(columns) => {
                taskTableRef?.current?.setTaskTableColumns(columns);
              }}
            />

            {/*<Button*/}
            {/*  key="add"*/}
            {/*  loading={false}*/}
            {/*  onClick={(e) => {*/}
            {/*    // 新建审核任务*/}
            {/*    taskCreateContainerRef?.current?.setReviewCreateStatus(true);*/}
            {/*  }}*/}
            {/*>*/}
            {/*  新建评审任务*/}
            {/*</Button>*/}
            {/* 20250604 九院先暂时关闭 没有装mongo那边 */}
            {/* <Dropdown
              trigger={['hover']}
              placement={'bottom'}
              menu={{
                onClick: onTaskDropdownClick,
                items: [
                  {
                    label: (
                      <Button type={'text'} key={'NUMBER_RANDOM'}>
                        自动添加病案
                      </Button>
                    ),
                    key: 'NUMBER_RANDOM',
                  },
                  {
                    label: (
                      <Button type={'text'} key={'MANUAL_SELECT'}>
                        查找病案添加
                      </Button>
                    ),
                    key: 'MANUAL_SELECT',
                  },
                ],
              }}
            >
              <Space className={'batch-add-dmr'}>
                添加评审病案
                <DownOutlined style={{ color: '#1890ff' }} />
              </Space>
            </Dropdown> */}
          </>
        }
      >
        <ReviewTable
          id={'dmr-review-card-progress-table'}
          selectedStatItem={selectedStatItem}
          searchParams={searchParams}
          taskTableRef={taskTableRef}
          dmrPreviewContainerRef={dmrPreviewContainerRef}
          extraLoading={taskAcceptAllLoading}
          scroll={{
            x: 'max-content',
            y: examineSummaryShow === false ? 505 : 310,
          }}
          cancelProps={{
            showCancel: true,
            dmrReviewerContainerRef: dmrReviewerContainerRef,
          }}
          reviewProps={{
            showAccept: true,
            dmrReviewerContainerRef: dmrReviewerContainerRef,
            showOtherExamineTask: true,
          }}
          summaryInfo={taskSummaryInfo}
          fastSelectDefaultKey={'Pending'}
          taskExtraParams={() => {
            return {
              reviewerCodes: getReviewerCodes(),
              ...searchParams,
            };
          }}
          extraHiddenColumns={dmrReviewColumns}
        />
      </Card>

      {/*新建审核任务*/}
      {/*ReviewTaskCreateContainer*/}
      {/* <ReviewTaskCreateContainerSimple
        taskCreateContainerRef={taskCreateContainerRef}
        batchId={searchParams?.BatchId}
        batchInfo={searchParams?.batchInfo}
        searchParams={searchParams}
        reviewerCode={getReviewerCode()}
        selectedReviewerItem={searchParams?.adminReviewerItem}
        onTaskCreateSuccess={() => {
          taskStatusSummaryReq();
          taskTableRef?.current?.freshQueryTable();
        }}
        selectMultiple={true}
      /> */}

      <ScoreCommentDrawerContainer
        tableReadonly={false}
        dmrReadonly={true}
        drawerContainerRef={dmrPreviewContainerRef}
        onScoreCommentReviewEnd={(taskId: number) => {
          taskStatusSummaryReq();
          taskTableRef?.current?.freshQueryTable();
          // taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        onScoreCommentClose={(taskId: number) => {
          taskStatusSummaryReq();
          taskTableRef?.current?.freshQueryTable();
          // taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        onScoreCommentTableRefresh={(taskId: number) => {
          // taskTableRef?.current?.queryTasksCurrent();
          taskStatusSummaryReq();
          // taskTableRef?.current?.freshQueryTable();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        getContainer={() => {
          return document.getElementById('reviewer-container');
        }}
      />
    </div>
  );
};

export default DmrReviewReviewer;
