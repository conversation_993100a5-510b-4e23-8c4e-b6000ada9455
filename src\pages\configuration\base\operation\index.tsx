import { UniTable } from '@uni/components/src';
import { operDictColumns } from '@/pages/configuration/base/columns';
import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Space,
  TableProps,
  message,
  Divider,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useRequest, useSelector } from 'umi';
import { RespVO, TableColumns, TableResp } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';
import OperationDictionaryAdd from './components/add';
import {
  operationDegreeProcessor,
  operationOperationTypeProcessor,
} from '@/pages/configuration/base/operation/processor';
import { OperDictionaryItem } from '@/pages/configuration/base/interfaces';
import { useModel } from '@@/plugin-model/useModel';
import { v4 as uuidv4 } from 'uuid';
import UniEditableTable from '@uni/components/src/table/edittable';
import { useDebounce } from 'ahooks';
import ConfigExcelTemplateHandler from '@/components/configExcelTemplateHandler';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { useUpdateEffect } from 'ahooks';
import ISD from '@uni/components/src/infinite-scroll';
import DebounceSelect from '@/components/debounceSelect';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';

interface OperationDictionaryProps {}
const Event = 'DmrOper';
const InfiniteScrollId = 'dmr-oper-isd';

const OperationDictionary = (props?: OperationDictionaryProps) => {
  const [form] = Form.useForm();
  const ref = useRef<any>();

  const dictData = useSelector((state) => state?.uniDict?.dictData?.Dmr);
  // keywords
  const [searchKeywords, setSearchKeywords] = useState('');
  const debouncedKeywords = useDebounce(searchKeywords, { wait: 1000 });

  const moduleGroup = 'Dmr';

  const [operDictionaryAdd, setOperDictionaryAdd] = useState(false);

  const [operDictionaryColumns, setOperDictionaryColumns] = useState([]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  // 几个infinite新值
  const [infiniteData, setInfiniteData] = useState([]);
  const [infiniteLoading, setInfiniteLoading] = useState(false);
  const [editIndex, setEditIndex] = useState(-1);

  useEffect(() => {
    if (dictData && !operDictionaryColumns.length) {
      operationConfigurationColumnsReq();
    }
  }, [dictData]);

  const { run: operationConfigurationColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetDmrOperWithAllCompares', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setOperDictionaryColumns(
            tableColumnBaseProcessor(
              operDictColumns(Event, dictData),
              response?.data?.Columns,
            ),
          );
        } else {
          setOperDictionaryColumns([]);
        }
      },
    },
  );

  const { loading: operationUpsertLoading, run: operationUpsertReq } =
    useRequest(
      (values) => {
        let data = {};

        data = {
          ...values,
          ModuleGroup: moduleGroup,
        };

        return uniCommonService('Api/Sys/CodeSys/UpsertDmrOperAllCompare', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<number>) => {
          if (response?.statusCode === 200) {
            setOperDictionaryAdd(false);

            return response?.data;
          }
        },
        onSuccess: (data, ret) => {
          // 现在这边hack 因为无限滚动 不能再重新获取数据 需要使用记录的index与编辑的结果
          // 最好的是upsert该接口返回后端结果，确保数据正确
          if (editIndex > -1 && data) {
            setInfiniteData(
              infiniteData?.map((d, i) => {
                return d?.OperId === data ? { ...d, ...ret?.at(0) } : d;
              }),
            );
            setEditIndex(-1); // reset
          } else if (editIndex === -1 && data) {
            // 新增的情况 之后讨论下怎么进行数据处理 应该是后端返回这个新增的当前位置？TODO
            Emitter.emit(EventConstant.INFINITE_SCROLL_RELOAD_FETCH);
          }
        },
      },
    );

  const onOperationDictionaryItemAdd = (values: any) => {
    operationUpsertReq(
      editIndex > -1
        ? {
            ...infiniteData?.find((d, i) => i === editIndex),
            ...values,
          }
        : values,
    );
  };

  const { run: icdeDeleteReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        ModuleGroup: moduleGroup,
        operId: values.OperId,
      };
      return uniCommonService('Api/Sys/CodeSys/DeleteDmrOper', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
          return response?.data;
        }
      },
      onSuccess: (data, params) => {
        if (data) {
          setInfiniteData(
            infiniteData?.filter((d, i) => d?.OperId !== params?.at(0)?.OperId),
          );
        }
      },
    },
  );

  useEffect(() => {
    Emitter.onMultiple(
      [
        `${ConfigurationEvents.DMR_INSURANCE_OPER_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_HQMS_OPER_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_WT_OPER_SELECT}#${Event}`,
      ],
      (data) => {
        let currentFormValue = form.getFieldsValue()?.[data?.id];
        if (currentFormValue) {
          // set form value
          Object.keys(data?.values)?.forEach((key) => {
            currentFormValue[key] = data?.values?.[key];
          });
          form.setFieldValue(data?.id, currentFormValue);
        }
      },
    );

    Emitter.on(ConfigurationEvents.DMR_OPER_EDIT, (data) => {
      if (data?.index > -1) {
        // index 要记录 会使用到
        form.setFieldsValue(data.record);
        setEditIndex(data?.index);
        setOperDictionaryAdd(true);
      }
    });

    Emitter.on(ConfigurationEvents.DMR_OPER_DELETE, (data) => {
      if (data?.index > -1) {
        icdeDeleteReq(data.record);
      }
    });

    return () => {
      Emitter.offMultiple([
        `${ConfigurationEvents.DMR_INSURANCE_OPER_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_HQMS_OPER_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_WT_OPER_SELECT}#${Event}`,
      ]);
      Emitter.off(ConfigurationEvents.DMR_OPER_EDIT);
      Emitter.off(ConfigurationEvents.DMR_OPER_DELETE);
    };
  }, [infiniteData]);

  // infinite 额外处理
  // debounce select
  async function fetchIcdeSelect(keyword: string): Promise<any[]> {
    if (!keyword) return;
    return await uniCommonService('Api/Dmr/DmrSearch/Oper', {
      params: {
        KeyWord: keyword,
        SkipCount: 0,
        MaxResultCount: 100,
      },
    });
  }

  // search emitter
  useEffect(() => {
    Emitter.on(
      EventConstant.INFINITE_SCROLL_CB_FETCH,
      async ({ searchedValue, cb, isdId, reqData }) => {
        if (isdId === InfiniteScrollId) {
          let response = await uniCommonService(
            'Api/Sys/CodeSys/SearchDmrOperWithAllCompare',
            {
              method: 'POST',
              data: {
                code: searchedValue,
              },
            },
          );
          if (response?.code === 0) {
            cb(response.data);
          }
        }
      },
    );

    return () => {
      Emitter.off(EventConstant.INFINITE_SCROLL_CB_FETCH);
    };
  }, []);

  return (
    <>
      <Card
        title="手术列表"
        extra={
          <Space>
            <ConfigExcelTemplateHandler
              downloadTemplateApiObj={{
                apiUrl: 'Api/Sys/CodeSys/GetDmrOperAllCompareExcelTemplate',
              }}
              downloadPostData={{
                moduleGroup,
                exportName: '手术列表',
              }}
              uploadXlsxApiObj={{
                apiUrl: 'Api/Sys/CodeSys/UploadDmrOperAllCompareExcelFile',
                onSuccess: () => {
                  Emitter.emit(EventConstant.INFINITE_SCROLL_RELOAD_FETCH);
                },
              }}
              uploadPostData={{
                moduleGroup,
              }}
            />
            <Button
              key="add"
              loading={false}
              onClick={(e) => {
                setOperDictionaryAdd(true);
                form.setFieldValue('IsValid', true);
              }}
            >
              新增手术
            </Button>
            <Divider type="vertical" />
            <TableColumnEditButton
              columnInterfaceUrl={'Api/Sys/CodeSys/GetDmrOperWithAllCompares'}
              onTableRowSaveSuccess={(columns) => {
                setOperDictionaryColumns(
                  tableColumnBaseProcessor(
                    operDictColumns(Event, dictData),
                    columns,
                  ),
                );
              }}
            />
          </Space>
        }
      >
        <UniTable
          actionRef={ref}
          id={`operation-dictionary-table`}
          className={'operation-dictionary-table'}
          rowKey={'uuidv4'}
          scroll={{ x: 'max-content', y: 540 }}
          headerTitle={
            <DebounceSelect
              value={searchKeywords}
              placeholder="请输入诊断编码"
              fetchOptions={fetchIcdeSelect}
              onChange={(newValue) => {
                setSearchKeywords(newValue);
              }}
              fieldNames={{
                label: 'Name',
                value: 'Code',
              }}
              style={{ width: '350px', marginBottom: '10px' }}
            />
          }
          toolBarRender={() => []}
          // backendPagination
          dictionaryData={dictData}
          widthDetectAfterDictionary
          bordered={true}
          loading={infiniteLoading || operationUpsertLoading}
          columns={operDictionaryColumns}
          dataSource={infiniteData} // operDictionaryTableDataSource
          clickable={false}
          pagination={false} // backPagination
        />
      </Card>
      <Modal
        title={editIndex > -1 ? '编辑手术' : '新增手术'}
        open={operDictionaryAdd}
        onOk={() => {
          onOperationDictionaryItemAdd(form.getFieldsValue());
        }}
        onCancel={() => {
          setOperDictionaryAdd(false);
        }}
        destroyOnClose={true}
        getContainer={document.getElementById('operation-dictionary-table')}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <OperationDictionaryAdd form={form} event={Event} />
      </Modal>

      <ISD
        id={InfiniteScrollId}
        scrollFetchObj={{
          url: 'Api/Sys/CodeSys/GetDmrOperWithAllCompares',
        }}
        scrollDom={document?.querySelector(
          `div[id='operation-dictionary-table'] div[class=ant-table-body]`,
        )}
        reqData={{
          Code: searchKeywords,
        }}
        dataSource={infiniteData}
        setDataSource={setInfiniteData}
        setLoading={setInfiniteLoading}
        searchedValue={searchKeywords}
        searchedKey="Code"
      />
    </>
  );
};

export default OperationDictionary;
