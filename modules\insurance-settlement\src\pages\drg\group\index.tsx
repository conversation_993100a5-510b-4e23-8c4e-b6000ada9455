import { Card, Col, Row, Tabs, Space } from 'antd';
import { useEffect, useState } from 'react';
import { useModel, useRequest } from 'umi';
import _ from 'lodash';
import './index.less';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import StatisticAnalysis from './components/statisticAnalysis';
import FeeChargeStatsDistribution from '../components/feeChargeStatsDestribution';
import ChsBmFeeTable from './components/chsBm';
import FeeChargeDistribution from '../components/feeChargeDistribution';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { UniSelect } from '@uni/components/src';
import {
  SettleCompStatsByCliDeptColumns,
  SettleCompStatsByGrpColumns,
  SettleCompStatsByMedTeamColumns,
  TabCommonItems,
} from '../constants';
import GradientChartAndTable from '../components/gradientChartAndTable/index';
import IconBtn from '@uni/components/src/iconBtn/index';
import { SearchOutlined } from '@ant-design/icons';
import { GrpQuadrantAxisOpts, GrpDefaultOpts } from '@/pages/drg/optsConstants';
import DetailTableModal from '@uni/components/src/detailed-table-modal/index';
import DrawerCardInfo from '../components/drawerCardInfo/index';
import { isEmptyValues } from '@uni/utils/src/utils';
import { mergeTableClickParams } from '@/utils/utils';

const ADrgAndGrpGroupAnalysis = () => {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { dateRange, hospCodes, insurType } = globalState?.searchParams;

  const [activeKey, setActiveKey] = useState('Drg');
  const [selectedTableItem, setSelectedTableItem] = useState(undefined);

  const [requestParams, setRequestParams] = useState(null);

  const [drawerVisible, setDrawerVisible] = useState(undefined);

  const [selectOpts, setSelectOpts] = useState([]);
  // tab 使用下拉框数据
  const {
    loading: getSettleCompStatsByEntityLoading,
    run: getSettleCompStatsByEntityReq,
  } = useRequest(
    (data) =>
      uniCommonService(
        'Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByGrp',
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          if (res?.data?.Stats?.length) {
            setSelectOpts(
              res.data.Stats?.map((d) => ({
                ...d,
                label: `${d?.ChsDrgCode} ${d?.ChsDrgName}`,
              })),
            );
            // 默认把第一个设置为selected
            if (!selectedTableItem) {
              setSelectedTableItem(
                _.maxBy(res?.data?.Stats, function (o) {
                  return o.PatCnt;
                }),
              );
            }
          } else {
            setSelectOpts([]);
          }
        }
      },
    },
  );

  useEffect(() => {
    if (
      (globalState?.searchParams &&
        globalState?.searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(requestParams) &&
        globalState?.searchParams?.dateRange?.length)
    ) {
      let tableParams = {
        Sdate: dateRange?.at(0),
        Edate: dateRange?.at(1),
        HospCode: hospCodes,
        insurType,
      };
      setRequestParams(tableParams);
      setTimeout(() => {
        getSettleCompStatsByEntityReq(tableParams);
      }, 500);
    }
  }, [globalState?.searchParams]);

  useEffect(() => {
    Emitter.on(EventConstant.SEARCH_DOWN_DRILL, (value) => {
      console.log(value);
      if (value) {
        setSelectedTableItem(
          selectOpts?.find((d) => d?.VersionedChsDrgCode === value),
        );
        // 并且切换tab
        setActiveKey('statistic');
      }
    });
    return () => {
      Emitter.off(EventConstant.SEARCH_DOWN_DRILL);
    };
  }, [selectOpts, selectedTableItem]);

  const tabs = [
    {
      key: 'Drg',
      label: 'DRG病组构成',
      children: (
        <GradientChartAndTable
          args={{
            level: 'hosp',
            // type: 'drg',
            isDrgTable: true,
            clickable: false,
            cols: 'col-xl-24',
            title: '病组效率',
            category: 'ChsDrgName',
            columns: requestParams
              ? [
                  {
                    dataIndex: 'operation',
                    visible: true,
                    width: 60,
                    align: 'center',
                    order: 1,
                    title: '',
                    render: (node, record, index) => {
                      return (
                        <Space>
                          <IconBtn
                            type="details"
                            customIcon={
                              <SearchOutlined
                                onPointerEnterCapture={() => {}}
                                onPointerLeaveCapture={() => {}}
                              />
                            }
                            title="下钻"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(
                                EventConstant.SEARCH_DOWN_DRILL,
                                record?.VersionedChsDrgCode,
                              );
                            }}
                          />
                          <IconBtn
                            type="details"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                id: 'drg-group-settle-stats-by-chsdrg', // 要匹配到对应的DetailTableModal id
                                title: record?.ChsDrgName,
                                args: {
                                  ...requestParams,
                                  VersionedChsDrgCodes: [
                                    record?.VersionedChsDrgCode || undefined,
                                  ],
                                },
                                type: 'drg',
                                detailsUrl:
                                  'FundSupervise/LatestDrgSettleStats/SettleDetails',
                                dictData: globalState?.dictData, // 传入
                              });
                            }}
                          />
                        </Space>
                      );
                    },
                  },
                  ...SettleCompStatsByGrpColumns,
                ]
              : [],
            defaultAxisOpt: GrpDefaultOpts,
            axisOpts: GrpQuadrantAxisOpts,
            api: 'Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByGrp',
            emitter: EventConstant.DRG_TABLE_ROW_CLICK,
          }}
          noChart
          requestParams={requestParams}
        />
      ),
    },
    {
      key: 'statistic',
      label: '支付指标统计',
      children: (
        <StatisticAnalysis type="grp" selectedTableItem={selectedTableItem} />
      ),
    },
    {
      key: 'fee_analysis',
      label: '费用构成分析',
      children: (
        <Tabs
          tabPosition="left"
          items={[
            {
              key: 'statsItems',
              label: '统计类别',
              children: (
                <>
                  <FeeChargeStatsDistribution
                    id="HospDrgFeePie"
                    level={'grp'}
                    title={'统计类别'}
                    selectedTableItem={selectedTableItem}
                    requestParams={requestParams}
                    api={`Api/FundSupervise/LatestDrgSettleStats/FeeChargeDistribution`}
                  />
                  <ChsBmFeeTable
                    groupCode={selectedTableItem?.ChsDrgCode}
                    chargeTypeMode={'Stats'}
                  />
                </>
              ),
            },
            {
              key: 'tarItems',
              label: '收费项目类别',
              children: (
                <>
                  <FeeChargeDistribution
                    id="HospDrgFeePie"
                    level={'grp'}
                    title={'收费项目类别'}
                    selectedTableItem={selectedTableItem}
                    requestParams={requestParams}
                    api={`Api/FundSupervise/LatestDrgSettleStats/FeeChargeDistribution`}
                  />
                  <ChsBmFeeTable
                    groupCode={selectedTableItem?.ChsDrgCode}
                    chargeTypeMode={'Med'}
                  />
                </>
              ),
            },
          ]}
        />
      ),
    },
    {
      key: TabCommonItems.majorPerfDeptAnalysis.key,
      label: '学科横向对比',
      children: (
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTable
                args={{
                  level: 'hospCode',
                  clickable: false,
                  cols: 'col-xl-24',
                  title: '学科对比分析',
                  category: 'MajorPerfDeptName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      fixed: 'left',
                      order: 1,
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                title: `${record?.MajorPerfDeptName}`,
                                args: {
                                  // 学科 特殊处理 需要按照record里面与requestParams合并传参
                                  ...mergeTableClickParams(
                                    requestParams,
                                    record,
                                    ['HospCode'],
                                  ),
                                  versionedChsDrgCodes:
                                    selectedTableItem?.VersionedChsDrgCode
                                      ? [selectedTableItem?.VersionedChsDrgCode]
                                      : [],
                                  MajorPerfDepts: record?.MajorPerfDept
                                    ? [record?.MajorPerfDept]
                                    : ['%'],
                                },
                                type: 'drg',
                                detailsUrl:
                                  'FundSupervise/LatestDrgSettleStats/SettleDetails',
                                dictData: globalState?.dictData, // 传入
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByCliDeptColumns,
                  ],
                  type: 'drg',
                  selectedTableItem,
                  // detailsTitle: '科室分布',
                  // defaultAxisOpt: CliDeptDefaultOpts,
                  // axisOpts: CliDeptQuadrantAxisOpts,
                  api: `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByMajorPerfDept`,
                }}
                noChart={true}
                requestParams={requestParams}
              />
            </Col>
          </Row>
        </>
      ),
    },
    // {
    //   key: 'dept_analysis',
    //   label: '科室横向对比',
    //   children: (
    //     <DeptAnalysisTable
    //       versionedChsDrgCode={selectedTableItem?.VersionedChsDrgCode}
    //     />
    //   ),
    // },
    {
      key: TabCommonItems.medTeamAnalysis.key,
      label: '医疗组横向对比',
      children: (
        // <MedTeamAnalysisTable
        //   versionedChsDrgCode={selectedTableItem?.VersionedChsDrgCode}
        // />
        <>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <GradientChartAndTable
                args={{
                  clickable: false,
                  cols: 'col-xl-24',
                  title: '医疗组对比分析',
                  category: 'MedTeamName',
                  columns: [
                    {
                      dataIndex: 'operation',
                      visible: true,
                      width: 40,
                      align: 'center',
                      fixed: 'left',
                      order: 1,
                      title: '',
                      render: (node, record, index) => {
                        return (
                          <IconBtn
                            type="details"
                            onClick={(e) => {
                              e.stopPropagation();
                              Emitter.emit(EventConstant.DETAILS_BTN_CLICK, {
                                id: 'drg-med-team-settle-stats-by-med-team', // 要匹配到对应的DetailTableModal id
                                title: record?.MedTeamName,
                                args: {
                                  ...requestParams,
                                  versionedChsDrgCodes:
                                    selectedTableItem?.VersionedChsDrgCode
                                      ? [selectedTableItem?.VersionedChsDrgCode]
                                      : [],
                                  MedTeams: record?.MedTeam
                                    ? [record?.MedTeam]
                                    : [''],
                                },
                                type: 'drg',
                                detailsUrl:
                                  'FundSupervise/LatestDrgSettleStats/SettleDetails',
                                dictData: globalState?.dictData, // 传入
                              });
                            }}
                          />
                        );
                      },
                    },
                    ...SettleCompStatsByMedTeamColumns,
                  ],
                  extraColumnsProcessor: (totalColumns) => {
                    return totalColumns?.map((d) => ({
                      ...d,
                      onCell: (record, rowIndex) => {
                        if (record?.KeyFactorName === d?.data) {
                          return {
                            style: {
                              color: '#1890ff',
                              fontWeight: 'bold',
                            },
                          };
                        }
                      },
                    }));
                  },
                  api: `Api/FundSupervise/LatestDrgSettleStats/SettleCompStatsByMedTeam`,
                  level: 'hospCode',
                  type: 'drg',
                  selectedTableItem,
                }}
                noChart={true}
                requestParams={requestParams}
              />
            </Col>
          </Row>
        </>
      ),
    },
  ];

  return (
    <>
      {/* <Tabs
        items={tabs}
        // onChange={(activeKeys) => {
        //   resizeCharts(activeKeys);
        //   // Emitter.emit(EventConstant.TABLE_ROW_CLICK, { record: {} });
        // }}
      /> */}
      <Card>
        <Tabs
          items={tabs}
          activeKey={activeKey}
          onChange={(activeKeys) => {
            setActiveKey(activeKeys);
          }}
          tabBarExtraContent={{
            right: activeKey !== 'Drg' && (
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <label>当前病组：</label>
                <UniSelect
                  width={300}
                  showSearch
                  dataSource={selectOpts}
                  value={selectedTableItem?.VersionedChsDrgCode}
                  onChange={(value) => {
                    setSelectedTableItem(
                      selectOpts?.find((d) => d?.VersionedChsDrgCode === value),
                    );
                  }}
                  allowClear={false}
                  enablePinyinSearch={true}
                  optionNameKey={'label'}
                  optionValueKey={'VersionedChsDrgCode'}
                />
              </div>
            ),
          }}
        />
        <DetailTableModal
          dictData={globalState.dictData}
          detailAction={(record) => {
            // 这里替代内部 操作 onClick
            setDrawerVisible({
              hisId: record?.HisId,
              type: 'drg',
            });
          }}
        />
        <DrawerCardInfo
          visible={drawerVisible}
          onClose={() => setDrawerVisible(undefined)}
        />
      </Card>
    </>
  );
};

export default ADrgAndGrpGroupAnalysis;
