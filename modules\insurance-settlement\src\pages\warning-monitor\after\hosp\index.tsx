import { Reducer, useEffect, useMemo, useReducer, useState } from 'react';
import { Col, Row, TableProps } from 'antd';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import { useModel } from '@@/plugin-model/useModel';
import { IReducer, ITableState } from '@uni/reducers/src/interface';
import { InitTableState, TableAction, tableReducer } from '@uni/reducers/src';
import './index.less';
import { useRequest } from 'umi';
import SingleStat from '@uni/components/src/statistic';
import { hospDeptWarningCntPie } from '../../chat.opts';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import TreeCtrlTable from '@uni/components/src/tree-ctrl-table';
import UniEcharts from '@uni/components/src/echarts';
import { useLocation } from 'umi';
import IconBtn from '@uni/components/src/iconBtn';
import DrawerCardInfo from '@/pages/drg/components/drawerCardInfo';
import { isEmptyValues } from '@uni/utils/src/utils';

const WarningMonitorHosp = () => {
  const location = useLocation();

  const {
    globalState: { dictData, searchParams, userInfo },
  } = useModel('@@qiankunStateFromMaster');

  // 记录一份tableParams
  const [tableParams, setTableParams] = useState(undefined);

  // drawer
  const [drawerVisible, setDrawerVisible] = useState(undefined);
  // 主table
  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<any> & { fetchParams: any }, IReducer>
  >(tableReducer, { ...InitTableState, fetchParams: undefined });

  const [abnormalStatsOpts, setAbnormalStatsOpts] = useState([]);
  const [warningStatsOpts, setWarningStatsOpts] = useState([]);
  // stats part 1
  const {
    data: stats,
    loading: statsFetchLoading,
    run: statsFetch,
  } = useRequest(
    (params) =>
      uniCommonService(
        `Api/FundSupervise/${
          location.pathname.includes('dip')
            ? 'LatestDipSettleStats'
            : 'LatestDrgSettleStats'
        }/SettleCompStatsOfHosp`,
        {
          method: 'POST',
          data: params,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.Stats?.at(0);
        }
        return null;
      },
    },
  );

  // tree-table
  // tree part
  const {
    data: treePart,
    loading: treePartFetchLoading,
    run: treePartFetch,
  } = useRequest(
    (data) =>
      uniCommonService(
        'Api/FundSupervise/LatestSettleCheckReport/GetSettleCheckStats',
        {
          method: 'POST',
          data: data,
        },
      ),
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 这里处理数据
          let { data } = res,
            treeData = [];
          if (data?.RuleStats && data?.RuleStats?.length > 0) {
            treeData = _.sortBy(data?.RuleStats, ['Sort'])?.map((v, i) => ({
              key: `key-${i}`,
              title: v.DisplayErrMsg,
              name: v.DisplayErrMsg,
              value: v.PatCnt,
              remark: v?.Remark,
              Sort: v?.Sort,
              args: {
                RuleCode: v.RuleCode,
              },
            }));
          }
          // 最后用 全部 将 数据包裹
          treeData = [
            {
              key: 'key-all',
              title: '全部',
              name: '全部',
              value: res.data?.ProblematicCardCnt,
              children: treeData,
            },
          ];
          setWarningStatsOpts([
            {
              name: '黄色异常',
              data: 'YellowWarningLevelPatCnt',
              value: data?.YellowWarningLevelPatCnt || 0,
              itemStyle: {
                color: '#f5d06c',
              },
              totalFee: data?.YellowWarningLevelTotalFee || 0,
            },
            {
              name: '橙色异常',
              data: 'OrangeWarningLevelPatCnt',
              value: data?.OrangeWarningLevelPatCnt || 0,
              itemStyle: {
                color: '#f59c6c',
              },
              totalFee: data?.OrangeWarningLevelTotalFee || 0,
            },
            {
              name: '红色异常',
              data: 'RedWarningLevelPatCnt',
              value: data?.RedWarningLevelPatCnt || 0,
              itemStyle: {
                color: '#f56c6c',
              },
              totalFee: data?.RedWarningLevelTotalFee || 0,
            },
          ]);
          return { ...res.data, treeDataResult: treeData };
        }
        setWarningStatsOpts([]);
        return { treeDataResult: [] };
      },
    },
  );
  // table-part:columns
  const { loading: tableColumnsFetchLoading, run: tableColumnsFetch } =
    useRequest(
      () =>
        uniCommonService(
          'Api/FundSupervise/LatestSettleCheckReport/GetSettleCheckCardDetails',
          {
            method: 'POST',
            headers: {
              'Retrieve-Column-Definitions': 1,
            },
          },
        ),
      {
        formatResult: (res) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            TableDispatch({
              type: TableAction.columnsChange,
              payload: {
                columns: tableColumnBaseProcessor([], res?.data?.Columns),
              },
            });
          }
          return null;
        },
      },
    );
  // table-part:datasource
  const {
    data: tablePart,
    loading: tablePartFetchLoading,
    run: tablePartFetch,
  } = useRequest(
    (data, pagi, sorter = null) =>
      uniCommonService(
        'Api/FundSupervise/LatestSettleCheckReport/GetSettleCheckCardDetails',
        {
          method: 'POST',
          data: {
            DtParam: {
              Draw: 1,
              Start: (pagi.cur - 1) * pagi.size,
              Length: pagi.size,
            },
            ...data,
            ruleCode: data?.ruleCode ?? '%',
          },
        },
      ),
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
        return null;
      },
      onSuccess: (data, params) => {
        if (data) {
          // data + pagi
          TableDispatch({
            type: TableAction.dataPagiChange,
            payload: {
              data: data?.data ?? [],
              backPagination: {
                ...TableState.backPagination,
                current: params?.at(1)?.cur,
                pageSize: params?.at(1)?.size,
                total: data?.recordsFiltered ?? 0,
              },
            },
          });
          // sorter
          if (!_.isEqual(params?.at(2), TableState.sorter)) {
            TableDispatch({
              type: TableAction.sortChange,
              payload: { sorter: params?.at(2) },
            });
          }
        }
      },
    },
  );

  // 处理树选中刷新table
  const handleTreeOnSelect = (keys: React.Key[], info: any) => {
    let { node } = info;
    TableDispatch({
      type: TableAction.anyChange, // 把fetchParams当作接口的部分参数记录，只记录要记的，比如searchParams的就别记
      payload: {
        title: node?.title,
        fetchParams: {
          ruleCode: node?.args?.RuleCode ?? '%',
          warningLevel: node?.args?.WarningLevel,
        },
      },
    });
    tablePartFetch(
      {
        ...tableParams,
        ruleCode: node?.args?.RuleCode ?? '%',
        warningLevel: node?.args?.WarningLevel?.toString(),
      },
      {
        cur: 1,
        size:
          TableState.backPagination?.pageSize ??
          TableState.backPagination?.defaultPageSize,
      },
    );
  };

  const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    tablePartFetch(
      {
        ...tableParams,
        ...(TableState?.fetchParams || {}),
      },
      { cur: pagi.current, size: pagi.pageSize },
    );
  };

  useEffect(() => {
    if (
      (searchParams && searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(tableParams) &&
        searchParams?.hospCodes?.length &&
        searchParams?.dateRange)
    ) {
      // hospCode: searchParams?.hospCodes,
      //           Sdate: searchParams?.dateRange?.at(0),
      //           Edate: searchParams?.dateRange?.at(1),
      //           Wards: searchParams?.wards,
      //           Coders: searchParams?.coders,
      let tableParams = {
        Sdate: searchParams?.dateRange?.at(0),
        Edate: searchParams?.dateRange?.at(1),
        HospCode: searchParams?.hospCodes,
        // drg dip 要做区分
        // CliDepts:
        //   searchParams?.CliDepts?.length > 0
        //     ? searchParams?.CliDepts
        //     : userInfo?.CliDepts,
        Wards:
          searchParams?.wards?.length > 0
            ? searchParams?.wards
            : userInfo?.Wards,
        Coders: searchParams?.coders,
        InsurType: searchParams?.insurType,
      };

      setTableParams(tableParams);
      statsFetch(tableParams);
      treePartFetch(tableParams);
      tablePartFetch(
        {
          ...tableParams,
          ...(TableState?.fetchParams || {}),
        },
        {
          cur: TableState.backPagination?.current,
          size:
            TableState.backPagination?.pageSize ??
            TableState.backPagination?.defaultPageSize,
        },
      );
    }
  }, [searchParams]);

  // // 异常人数chart
  // useEffect(() => {
  //   if (stats && treePart?.RuleStats) {
  //     setAbnormalStatsOpts([
  //       {
  //         name: '低倍率',
  //         key: 'LowPatCnt',
  //         value: stats?.LowPatCnt,
  //       },
  //       {
  //         name: '高倍率',
  //         key: 'HighPatCnt',
  //         value: stats?.HighPatCnt,
  //       },
  //       ...treePart?.RuleStats?.map((d) => ({
  //         name: d.DisplayErrMsg,
  //         key: d.RuleCode,
  //         value: d.PatCnt,
  //       })),
  //     ]);
  //   }
  // }, [stats, treePart]);

  const columns = useMemo(() => {
    if (TableState?.columns?.length > 0) {
      return [
        {
          dataIndex: 'operation',
          visible: true,
          width: 40,
          align: 'center',
          fixed: 'left',
          title: '',
          order: 1,
          render: (node, record, index) => {
            return (
              <IconBtn
                title="查看病案首页"
                type="details2"
                onClick={(e) => {
                  setDrawerVisible({
                    hisId: record?.HisId,
                    type: location.pathname.includes('dip') ? 'dip' : 'drg',
                  });
                }}
              />
            );
          },
        },
        ...TableState.columns,
      ];
    }
    return [];
  }, [TableState.columns]);

  return (
    <>
      <Row gutter={[8, 8]} style={{ marginBottom: '8px' }}>
        <Col span={7}>
          <SingleStat
            className="warning_stats"
            loading={statsFetchLoading}
            title="出院病人"
            suffix="人"
            value={stats?.PatCnt ?? 0}
            footerTitle="超支人次"
            footerValue={stats?.ProfitLossPatCnt ?? 0}
          />
        </Col>
        <Col span={7}>
          <SingleStat
            className="warning_stats"
            title="盈亏"
            dataType="Currency"
            loading={statsFetchLoading}
            suffix="元"
            prefix="￥"
            value={stats?.Profit ?? 0}
            footerTitle="总费用"
            footerValue={stats?.TotalFee ?? 0}
            footerDataType="Currency"
          />
        </Col>
        <Col span={10}>
          <SingleStat
            className="warning_chart_stats"
            title="异常等级"
            noValue={true}
            loading={statsFetchLoading}
            prefix={
              <UniEcharts
                height={133}
                width={'100%'}
                elementId="warning_cnt_bar"
                options={
                  (stats && hospDeptWarningCntPie(warningStatsOpts)) || {}
                }
              />
            }
          />
        </Col>
      </Row>

      {/* 底部的tree+table */}
      <TreeCtrlTable
        treeData={{
          title: '分类异常明细',
          subTitle: '问题病例数',
          subKey: 'ProblematicCardCnt',
          data: {
            ...treePart,
          },
        }}
        treeLoading={treePartFetchLoading}
        treeAction={{
          onSelect: handleTreeOnSelect,
        }}
        tableData={{
          columns: columns,
          dataSource: TableState.data,
          rowKey: 'Id',
          id: 'warning_hosp_table',
          loading: tablePartFetchLoading,
          pagination: TableState.backPagination,
          onChange: backTableOnChange,
          dictionaryData: dictData,
          // widthCalculate: true,
          widthDetectAfterDictionary: true,
          scroll: {
            x: 'max-content',
            y:
              document.getElementById('site-layout-content')?.offsetHeight -
              50 -
              30 -
              50 -
              30 -
              50 -
              50 -
              24,
          },
          // 3个额外的
          tableTitle: TableState.title,
          export: {
            isBackend: true,
            backendObj: {
              url: 'Api/FundSupervise/LatestSettleCheckReport/ExportGetSettleCheckCardDetails',
              method: 'POST',
              data: {
                ...tableParams,
                ...(TableState?.fetchParams || {}),
              },
              fileName: TableState.title as string,
            },
            btnDisabled: TableState.data?.length < 1,
          },
          columnEdit: {
            columnInterfaceUrl:
              'Api/FundSupervise/LatestSettleCheckReport/GetSettleCheckCardDetails',
            onTableRowSaveSuccess: (columns) => {
              TableDispatch({
                type: TableAction.columnsChange,
                payload: {
                  columns: tableColumnBaseProcessor([], columns),
                },
              });
            },
          },
        }}
      />

      <DrawerCardInfo
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
      />
    </>
  );
};

export default WarningMonitorHosp;
