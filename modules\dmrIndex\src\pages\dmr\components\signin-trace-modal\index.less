.signin-trace-input-container {
  width: 150px;
  font-size: 16px !important;
}
.signin-trace-input-container:focus-within {
  border: 1px solid #f4a741 !important;
  border-radius: 2px;
  background-color: #fdf6dd !important;
}

// 适合Modal内展示的时间轴样式
.modal-timeline-container {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 16px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background-color: #fafafa;

  .ant-timeline {
    margin-top: 0;

    .ant-timeline-item {
      padding-bottom: 12px;

      .ant-timeline-item-content {
        margin-left: 24px;

        p {
          margin: 0 0 4px 0;
          font-size: 13px;
          line-height: 1.4;

          &:first-child {
            font-weight: 500;
            color: #262626;
          }

          &:last-child {
            color: #595959;
          }
        }

        .ant-tag {
          font-size: 12px;
          margin-left: 8px;
          padding: 2px 6px;
          border-radius: 4px;
        }
      }
    }
  }

  // 空状态样式
  .empty-timeline {
    text-align: center;
    color: #8c8c8c;
    padding: 20px;
    font-size: 14px;
  }

  // 加载状态
  .timeline-loading {
    text-align: center;
    padding: 20px;
  }
}

.modal-timeline-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
}
