import { useEffect } from 'react';
import { doLog } from '@uni/services/src';
import { LogLevel } from '@uni/services/src/commonService';

const useTabTracker = (tabId: string) => {
  useEffect(() => {
    const TABS_KEY = 'activeTabs';

    // Register tab
    const registerTab = () => {
      const tabs = JSON.parse(localStorage.getItem(TABS_KEY)) || [];
      tabs.push(tabId);
      localStorage.setItem(TABS_KEY, JSON.stringify(tabs));
    };

    // Unregister tab
    const unregisterTab = () => {
      const tabs = JSON.parse(localStorage.getItem(TABS_KEY)) || [];
      const updatedTabs = tabs.filter((id) => id !== tabId);
      localStorage.setItem(TABS_KEY, JSON.stringify(updatedTabs));
      // If no more tabs, perform final action
      if (updatedTabs.length === 0) {
        console.log('All tabs closed!');
        doLog(
          LogLevel.Trace,
          JSON.stringify({
            title: 'tab关闭登出',
            token: localStorage.getItem('uni-connect-token'),
          }),
        );
        // Perform cleanup or API call here
        localStorage.removeItem('uni-connect-token');
        localStorage.removeItem('uni-refresh-token');
        localStorage.removeItem('expireItem');
        localStorage.removeItem('uni-connect-timestamp');
      }
    };

    // Listen to tab close
    window.addEventListener('beforeunload', unregisterTab);

    // Initial register
    registerTab();

    // Cleanup
    return () => {
      window.removeEventListener('beforeunload', unregisterTab);
      unregisterTab();
    };
  }, []);
};

export default useTabTracker;
