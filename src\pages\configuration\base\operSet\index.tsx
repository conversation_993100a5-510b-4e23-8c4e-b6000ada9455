import {
  dmrCliDeptsDictColumns,
  dmrOperSetColumns,
} from '@/pages/configuration/base/columns';
import {
  Button,
  Form,
  Modal,
  TableProps,
  Card,
  Space,
  Select,
  Col,
  message,
  Divider,
} from 'antd';
import React, {
  CSSProperties,
  FunctionComponent,
  MouseEventHandler,
  Reducer,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import { useRequest } from 'umi';
import { RespVO, TableColumns, TableResp } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';
import { EscalateCliDeptItem } from '@/pages/configuration/escalate/interfaces';
import { v4 as uuidv4 } from 'uuid';
import UniEditableTable from '@uni/components/src/table/edittable';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { typesProcessor } from '@/pages/configuration/escalate/icde/processor';
import ConfigExcelTemplateHandler from '@/components/configExcelTemplateHandler';
import {
  CloseCircleFilled,
  PlusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import {
  InitModalState,
  modalReducer,
  ModalAction,
} from '@uni/reducers/src/modalReducer';
import { IModalState, IReducer } from '@uni/reducers/src/interface';
import {
  ProForm,
  ProFormInstance,
  ProFormSelect,
  ProFormSwitch,
  ProFormText,
} from '@uni/components/src/pro-form/index';
import ConfigurationOperationSelect from '../components/oper-select/index';
import AsyncSelect from 'react-select/async';
import {
  MultiValueProps,
  components,
  MultiValueGenericProps,
  Props,
  OnChangeValue,
  ClearIndicatorProps,
} from 'react-select';
import {
  SortableContainer,
  SortableContainerProps,
  SortableElement,
  SortableHandle,
  SortEndHandler,
} from 'react-sortable-hoc';
import debounce from 'lodash/debounce';
import { UniDragEditOnlyTable, UniTable } from '@uni/components/src/index';
import { OperSetEventConstants } from './constants';
import _ from 'lodash';
import { OperColumns } from './columns';
import { getMinusCompareCollectionsWithDoubleCollections } from '../../utils';
import { useModel } from '@@/plugin-model/useModel';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';

const moduleGroup = 'Dmr';

const oper_set_oper_table = 'oper_set_oper_table';

// Sortable Select
const debounceTimeout = 800;
function arrayMove<T>(array: readonly T[], from: number, to: number) {
  const slicedArray = array.slice();
  slicedArray.splice(
    to < 0 ? array.length + to : to,
    0,
    slicedArray.splice(from, 1)[0],
  );
  return slicedArray;
}

const SortableMultiValue = SortableElement((props: MultiValueProps<any>) => {
  // this prevents the menu from being opened/closed when the user clicks
  // on a value to begin dragging it. ideally, detecting a click (instead of
  // a drag) would still focus the control and toggle the menu, but that
  // requires some magic with refs that are out of scope for this example
  const onMouseDown: MouseEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };
  const innerProps = { ...props.innerProps, onMouseDown };
  return <components.MultiValue {...props} innerProps={innerProps} />;
});

const SortableMultiValueLabel = SortableHandle(
  (props: MultiValueGenericProps) => {
    return (
      <components.MultiValueLabel {...props}>
        {props?.data?.value}
      </components.MultiValueLabel>
    );
  },
);

const SortableSelect = SortableContainer(AsyncSelect) as React.ComponentClass<
  Props<any, true> & SortableContainerProps
>;

// const ClearIndicator = (
//   props: ClearIndicatorProps<any, true>
// ) => {
//   return (
//     <components.ClearIndicator {...props}>
//       <CloseCircleFilled/>
//     </components.ClearIndicator>
//   );
// };
const ClearIndicator = (props: ClearIndicatorProps<any, true>) => {
  const {
    children = <CloseCircleFilled />,
    getStyles,
    innerProps: { ref, ...restInnerProps },
  } = props;
  return (
    <div
      {...restInnerProps}
      ref={ref}
      style={getStyles('clearIndicator', props) as CSSProperties}
    >
      <div style={{ padding: '0px 5px' }}>{children}</div>
    </div>
  );
};

const OperationSet = () => {
  const { globalState } = useModel('@@qiankunStateForSlave');

  const ref = useRef<any>();

  const [form] = ProForm.useForm<ProFormInstance>();

  const [ModalState, ModalStateDispatch] = useReducer<
    Reducer<IModalState<any>, IReducer>
  >(modalReducer, InitModalState);

  const [
    departmentDictionaryTableDataSource,
    setDepartmentDictionaryTableDataSource,
  ] = useState([]);

  const [departmentDictionaryColumns, setDepartmentDictionaryColumns] =
    useState([]);

  useEffect(() => {
    departmentConfigurationColumnsReq();
    departmentDictionaryReq();
  }, []);

  useEffect(() => {
    Emitter.on(ConfigurationEvents.OPER_SET_EDIT, (record) => {
      ModalStateDispatch({
        type: ModalAction.change,
        payload: {
          visible: true,
          record,
        },
      });
      form.setFieldsValue(record);
      // 通过RelatedOperCodes 获取 Name 并拼起来
      operReorderReq(record?.RelatedOperCodes);
      // setOpers([
      //   ..._.sortBy(
      //     res?.data?.Opers?.map((d) => ({
      //       ...d,
      //       Id: d?.Code + uuidv4(),
      //     })),
      //     'OperSort',
      //   ),
      //   {
      //     Id: 'ADD',
      //   },
      // ]);
      // setSelected(
      //   record?.RelatedOperCodes?.map((d) => ({ label: d, value: d })),
      // );
    });

    Emitter.on(ConfigurationEvents.OPER_SET_DELETE, (record) => {
      departmentDeleteReq(record?.DmrOperComboId);
    });

    return () => {
      Emitter.off(ConfigurationEvents.OPER_SET_EDIT);
      Emitter.off(ConfigurationEvents.OPER_SET_DELETE);
    };
  }, [departmentDictionaryTableDataSource]);

  const { loading: departmentDictionaryLoading, run: departmentDictionaryReq } =
    useRequest(
      () => {
        return uniCommonService('Api/Sys/CodeSys/GetDmrOperCombos', {
          method: 'POST',
          data: {},
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<EscalateCliDeptItem[]>) => {
          if (response.code === 0 && response?.statusCode === 200) {
            let tableDataSource = response?.data?.slice();
            setDepartmentDictionaryTableDataSource(tableDataSource);
          } else {
            setDepartmentDictionaryTableDataSource([]);
          }
        },
      },
    );

  const { run: departmentConfigurationColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetDmrOperCombos', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setDepartmentDictionaryColumns(
            tableColumnBaseProcessor(
              dmrOperSetColumns,
              response?.data?.Columns,
            ),
          );
        } else {
          setDepartmentDictionaryColumns([]);
        }
      },
    },
  );

  const { loading: departmentUpsertLoading, run: departmentUpsertReq } =
    useRequest(
      (values) => {
        let data = {};

        data = {
          ...values,
        };

        return uniCommonService('Api/Sys/CodeSys/UpsertDmrOperCombo', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<number>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            ModalStateDispatch({
              type: ModalAction.init,
            });
            message.success('操作成功');
            // setSelected([]);
            // setInput('');
            departmentDictionaryReq();
          }
          return response;
        },
      },
    );

  const { loading: departmentDeleteLoading, run: departmentDeleteReq } =
    useRequest(
      (id) => {
        let data = {};

        data = {
          dmrOperComboId: id,
        };

        return uniCommonService('Api/Sys/CodeSys/DeleteDmrOperCombo', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<number>) => {
          if (response?.code === 0 && response?.statusCode === 200) {
            message.success('操作成功');
            departmentDictionaryReq();
          }

          return response;
        },
      },
    );

  // /DmrSearch/OperReorder
  const { loading: operReorderLoading, run: operReorderReq } = useRequest(
    (data) => {
      return uniCommonService('Api/Dmr/DmrSearch/OperReorder', {
        method: 'GET',
        params: {
          OperCodes: data,
          KeyWord: 'OperationCombo',
          StrictMode: true,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<any>) => {
        if (response.code === 0) {
          return response?.data?.Data;
        } else {
          return [];
        }
      },
      onSuccess: (data, params) => {
        console.log('huihiu', [
          ...params?.at(0)?.map((d) => {
            let d3 = data?.find((d2) => d2.Code === d);
            return {
              ...d3,
              Id: d + uuidv4(),
            };
          }),
          {
            Id: 'ADD',
          },
        ]);
        setOpers([
          ...params?.at(0)?.map((d) => {
            let d3 = data?.find((d2) => d2.Code === d);
            return {
              ...d3,
              Id: d + uuidv4(),
            };
          }),
          {
            Id: 'ADD',
          },
        ]);
      },
    },
  );

  // // Sortable Select part
  // const [selected, setSelected] = React.useState<readonly any[]>([]);
  // const [input, setInput] = useState('');

  // // debounce select
  // const loadOptions = async (keyword) => {
  //   let { data } = await uniCommonService('Api/Dmr/DmrSearch/Oper', {
  //     params: {
  //       KeyWord: keyword,
  //       SkipCount: 0,
  //       MaxResultCount: 100,
  //     },
  //   });
  //   return data?.Data?.map((d) => ({
  //     label: `${d.Code} ${d.Name}`,
  //     value: d.Code,
  //   }));
  // };

  // const onChange = (selectedOptions: OnChangeValue<any, true>) => {
  //   setSelected(selectedOptions);
  // };

  // const onSortEnd: SortEndHandler = ({ oldIndex, newIndex }) => {
  //   const newValue = arrayMove(selected, oldIndex, newIndex);
  //   setSelected(newValue);
  //   // console.log(
  //   //   'Values sorted:',
  //   //   newValue.map((i) => i.value),
  //   // );
  // };

  // oper handler
  const [opers, setOpers] = useState([]);
  const [operForm] = Form.useForm();
  // emitter: oper
  useEffect(() => {
    // 每次oper变化的时候 setForm
    form.setFieldValue(
      'RelatedOperCodes',
      opers?.filter((d) => d.Id !== 'ADD')?.map((d) => d?.Code),
    );

    Emitter.on(
      OperSetEventConstants.OPER_TABLE_LIKE_ROW_CLICK,
      ({ rowRecord, record, index }) => {
        if (record?.Id === 'ADD') {
          let temp = [
            ...opers?.filter((d) => d?.Id !== 'ADD'),
            {
              Id: uuidv4(),
              OperSort: opers?.length,
              InsurName: rowRecord?.InsurName,
              InsurCode: rowRecord?.InsurCode,
              Code: rowRecord?.Code,
              Name: rowRecord?.Name,
            },
          ];
          setOpers([..._.cloneDeep(temp), { Id: 'ADD' }]);
        } else {
          let temp = [
            ...opers
              ?.filter((d) => d?.Id !== 'ADD')
              ?.map((item, idx) => {
                return idx === index
                  ? {
                      OperSort: item?.OperSort,
                      Id: uuidv4(),
                      InsurCode: rowRecord?.InsurCode,
                      InsurName: rowRecord?.InsurName,
                      Code: rowRecord?.Code,
                      Name: rowRecord?.Name,
                    }
                  : item;
              }),
          ];
          setOpers([..._.cloneDeep(temp), { Id: 'ADD' }]);
        }
      },
    );

    Emitter.on(
      OperSetEventConstants.OPER_TABLE_LIKE_CLEAR,
      ({ record, index }) => {
        let temp = opers?.slice();
        temp.splice(index, 1);
        setOpers(
          temp?.map((d, i) => ({
            ...d,
            OperSort: i,
            // IsMain: i === 0 ? true : false,
          })),
        );
      },
    );

    return () => {
      Emitter.off(OperSetEventConstants.OPER_TABLE_LIKE_ROW_CLICK);
      Emitter.off(OperSetEventConstants.OPER_TABLE_LIKE_CLEAR);
    };
  }, [opers]);

  return (
    <>
      <Card
        title="手术组套列表"
        extra={
          <Space>
            <Button
              type="primary"
              icon={
                <PlusOutlined
                  onPointerEnterCapture={undefined}
                  onPointerLeaveCapture={undefined}
                />
              }
              onClick={() => {
                ModalStateDispatch({
                  type: ModalAction.change,
                  payload: {
                    visible: true,
                  },
                });
              }}
            >
              新建
            </Button>
            <Divider type="vertical" />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: 'Api/Sys/CodeSys/GetDmrOperCombos',
                onTableRowSaveSuccess: (columns) => {
                  setDepartmentDictionaryColumns(
                    tableColumnBaseProcessor(dmrOperSetColumns, columns),
                  );
                },
              }}
            />
          </Space>
        }
      >
        <UniTable
          actionRef={ref}
          id={`department-dictionary-table`}
          className={'escalate-department-dictionary-table'}
          rowKey={'id'}
          scroll={{
            y:
              document.getElementById('content')?.offsetHeight -
              20 -
              40 -
              32 -
              32 -
              28,
          }}
          bordered={true}
          loading={departmentDictionaryLoading || departmentUpsertLoading}
          columns={departmentDictionaryColumns}
          dataSource={departmentDictionaryTableDataSource}
        />
      </Card>

      <Modal
        title="手术组套"
        open={ModalState?.visible}
        style={{ top: 20 }}
        width={600}
        destroyOnClose
        onCancel={() => {
          ModalStateDispatch({
            type: ModalAction.init,
          });
          // setSelected([]);
          // setInput('');
        }}
        onOk={() => {
          form.submit();
        }}
      >
        <ProForm
          form={form}
          preserve={false}
          grid={true}
          layout={'vertical'}
          submitter={false}
          onFinish={async (values) => {
            console.log('onfinish', values);
            departmentUpsertReq({
              ...values,
              DmrOperComboId: ModalState?.record?.DmrOperComboId,
            });
          }}
        >
          <ProFormText
            name="Code"
            label="手术组套编码"
            placeholder="请输入手术组套编码"
            rules={[{ required: true, message: '请输入手术组套编码' }]}
            colProps={{ span: 12 }}
          />
          <ProFormText
            name="Name"
            label="手术组套名称"
            placeholder="请输入手术组套名称"
            rules={[{ required: true, message: '请输入手术组套名称' }]}
            colProps={{ span: 12 }}
          />
          <ProFormSelect
            name="RelatedDepts"
            label="科室"
            placeholder="请选择科室"
            // rules={[{ required: true, message: '请选择科室' }]}
            colProps={{ span: 24 }}
            mode="multiple"
            options={globalState?.dictData?.CliDepts}
            fieldProps={{
              fieldNames: {
                label: 'Name',
                value: 'Code',
              },
            }}
          />
          <Col span={24}>
            <ProForm.Item
              name="RelatedOperCodes"
              label="组套手术编码"
              rules={[{ required: true, message: '请选择关联的手术编码' }]}
            >
              {/* <SortableSelect
                useDragHandle
                // react-sortable-hoc props:
                axis="xy"
                helperClass="opt-dragging"
                onSortEnd={onSortEnd}
                // small fix for https://github.com/clauderic/react-sortable-hoc/pull/352:
                getHelperDimensions={({ node }) => node.getBoundingClientRect()}
                // react-select props:
                isMulti
                isClearable
                cacheOptions
                loadOptions={loadOptions}
                noOptionsMessage={() => '暂无数据，请输入关键字搜索'}
                value={selected}
                onChange={onChange}
                components={{
                  // @ts-ignore
                  MultiValue: SortableMultiValue,
                  // @ts-ignore
                  MultiValueLabel: SortableMultiValueLabel,
                  ClearIndicator,
                }}
                styles={{
                  container: (base) => ({
                    ...base,
                    minHeight: '32px !important',
                  }),
                  control: (base) => ({
                    ...base,
                    minHeight: '32px !important',
                    borderRadius: '2px',
                    borderColor: 'rgb(217 217 217)',
                    ':hover': {
                      borderColor: '#3d87ff',
                    },
                  }),
                  valueContainer: (base) => ({
                    ...base,
                    minHeight: '32px !important',
                  }),
                  indicatorsContainer: (base) => ({
                    ...base,
                    minHeight: '32px !important',
                  }),
                  indicatorSeparator: (base) => ({
                    ...base,
                    display: 'none',
                  }),
                  dropdownIndicator: (base) => ({
                    ...base,
                    display: 'none',
                  }),
                  clearIndicator: (base) => ({
                    ...base,
                    fontSize: '12px',
                  }),
                  placeholder: (base) => ({
                    ...base,
                    color: '#bfbfbf',
                  }),
                }}
                placeholder="请选择关联的手术编码"
                closeMenuOnSelect={false}
                inputValue={input}
                onInputChange={(value, action) => {
                  if (action.action === 'input-change') {
                    setInput(value);
                  }
                  if (action.action === 'set-value') {
                    return input;
                  }
                  if (action.action === 'menu-close') {
                    return action.prevInputValue;
                  }
                }}
              /> */}
              <UniDragEditOnlyTable
                form={operForm}
                key={oper_set_oper_table}
                id={oper_set_oper_table}
                tableId={oper_set_oper_table}
                scroll={{ y: 350 }}
                pagination={false}
                rowKey="Id"
                columns={OperColumns}
                dataSource={opers}
                onTableDataSourceOrderChange={(
                  tableData,
                  oldIndex,
                  newIndex,
                ) => {
                  console.log('tableData', tableData);
                  setOpers(
                    tableData?.map((d, i) => ({
                      ...d,
                      IcdeSort: i,
                      // IsMain: i === 0 ? true : false,
                    })),
                  );
                }}
              />
            </ProForm.Item>
          </Col>

          <Col span={24}></Col>
          {/* <ProFormSelect
            name="RelatedOperCodes"
            label="关联的手术编码"
            placeholder="请选择关联的手术编码"
            mode="multiple"
            rules={[{ required: true, message: '请选择关联的手术编码' }]}
            options={options}
          /> */}
          <ProFormText
            name="Remark"
            label="备注"
            placeholder="请输入备注"
            colProps={{ span: 12 }}
          />
          <ProFormSwitch
            name="IsValid"
            label="是否有效"
            initialValue={true}
            colProps={{ span: 12 }}
          />
        </ProForm>
      </Modal>
    </>
  );
};

export default OperationSet;
