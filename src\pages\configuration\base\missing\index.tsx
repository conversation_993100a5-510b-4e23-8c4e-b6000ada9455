import UniEditableTable from '@uni/components/src/table/edittable';
import { Button, Card, Form, message, Divider, TableProps, Space } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { missingDictColumns } from '@/pages/configuration/base/columns';
import { useRequest } from '@@/plugin-request/request';
import { uniCommonService } from '@uni/services/src';
import { RespVO, TableColumns } from '@uni/commons/src/interfaces';
import { TodoItem } from '@/pages/configuration/base/interfaces';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';
import './index.less';

const BaseConfigurationMissing = () => {
  const [form] = Form.useForm();
  const ref = useRef<any>();

  const [
    missingDictionaryTableDataSource,
    setMissingDictionaryTableDataSource,
  ] = useState([]);

  const [missingDictionaryColumns, setMissingDictionaryColumns] = useState([]);

  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  const [frontPagination, setFrontPagination] = useState({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
  });
  // 前端分页OnChange
  const frontTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setFrontPagination({
      ...frontPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });
  };

  useEffect(() => {
    // missingDictionaryColumnsReq();
    missingDictionaryReq();

    Emitter.on(ConfigurationEvents.MISSING_DELETE, (data) => {
      if (data?.TodoId) {
        deleteMissingDictionaryReq({
          TodoId: data?.TodoId,
          ModuleGroup: data?.ModuleGroup,
        });
      } else {
        message.error('TodoId缺失，请检查');
      }
    });

    Emitter.on(ConfigurationEvents.MISSING_CODE_SELECT, (data) => {
      let formItem = form?.getFieldValue(data?.id);
      formItem['Code'] = data?.values?.Code;
      formItem['Name'] = data?.values?.Name;
      form.setFieldValue(data?.id, formItem);
    });

    return () => {
      Emitter.off(ConfigurationEvents.MISSING_DELETE);
      Emitter.off(ConfigurationEvents.MISSING_CODE_SELECT);
    };
  }, []);

  const { loading: missingDictionaryLoading, run: missingDictionaryReq } =
    useRequest(
      () => {
        let data = {
          ModuleGroup: 'Dmr',
        };
        return uniCommonService(
          'Api/Sys/CodeSys/GetCodeDictionaryCompareTodos',
          {
            method: 'POST',
            data: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<TodoItem[]>) => {
          if (response.code === 0 && response?.statusCode === 200) {
            setMissingDictionaryTableDataSource(response?.data?.slice());
          } else {
            setMissingDictionaryTableDataSource([]);
          }
        },
      },
    );

  const { run: missingDictionaryColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetCodeDictionaryCompareTodos', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setMissingDictionaryColumns(
            tableColumnBaseProcessor(
              missingDictColumns,
              response?.data?.Columns,
            ),
          );
        } else {
          setMissingDictionaryColumns([]);
        }
      },
    },
  );

  const {
    loading: resolveMissingDictionaryLoading,
    run: resolveMissingDictionaryReq,
  } = useRequest(
    (values) => {
      return uniCommonService(
        'Api/Sys/CodeSys/ResolveCodeDictionaryCompareTodo',
        {
          method: 'POST',
          data: values,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.statusCode === 200) {
          message.success('更新成功');
          missingDictionaryReq();
        } else {
          message.error('更新失败');
        }
      },
    },
  );

  const {
    loading: deleteMissingDictionaryLoading,
    run: deleteMissingDictionaryReq,
  } = useRequest(
    (values) => {
      return uniCommonService(
        'Api/Sys/CodeSys/DeleteCodeDictionaryCompareTodo',
        {
          method: 'POST',
          data: values,
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.statusCode === 200) {
          message.success('删除成功');
          missingDictionaryReq();
        } else {
          message.error('删除失败');
        }
      },
    },
  );

  return (
    <div className={'base-configuration-missing-container'}>
      <Card
        title="缺失配置列表"
        // extra={
        //   <Space>
        //     <TableColumnEditButton
        //       {...{
        //         columnInterfaceUrl:
        //           'Api/Sys/CodeSys/GetCodeDictionaryCompareTodos',
        //         onTableRowSaveSuccess: (columns) => {
        //           setMissingDictionaryColumns(
        //             tableColumnBaseProcessor(missingDictColumns, columns),
        //           );
        //         },
        //       }}
        //     />
        //   </Space>
        // }
      >
        <UniEditableTable
          actionRef={ref}
          id={`base-configuration-missing-table`}
          className={'base-configuration-missing-table'}
          rowKey={'TodoId'}
          scroll={{ y: 540 }}
          bordered={true}
          loading={
            missingDictionaryLoading ||
            resolveMissingDictionaryLoading ||
            deleteMissingDictionaryLoading
          }
          columns={missingDictColumns}
          value={missingDictionaryTableDataSource}
          clickable={false}
          pagination={frontPagination}
          onTableChange={frontTableOnChange}
          recordCreatorProps={false}
          editable={{
            form: form,
            type: 'multiple',
            editableKeys: editableColumnKeys,
            onSave: async (rowKey, data, row) => {
              // operationUpsertReq(data);
              let formItem = form?.getFieldValue(data?.TodoId);
              if (formItem) {
                resolveMissingDictionaryReq({
                  Code: formItem?.Code,
                  Name: formItem?.Name,
                  TodoId: data?.TodoId,
                  ModuleGroup: data?.ModuleGroup,
                });
              }
            },
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.save, defaultDoms.cancel];
            },
            onChange: setEditableColumnKeys,
          }}
        />
      </Card>
    </div>
  );
};

export default BaseConfigurationMissing;
