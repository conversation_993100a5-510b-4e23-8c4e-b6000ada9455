import { PropertyItem } from '@/pages/configuration/interfaces';
import { UniSelectProperties } from '@/pages/configuration/properties/select';
import { PropertyItemConstants } from '@/pages/configuration/constants';

export const TimeRangeStatsProperties: PropertyItem[][] = [
  [
    {
      key: 'data.props.hourOnly',
      label: '是否仅输入小时',
      component: 'Switch',
      fieldProps: {},
    },
  ],
  [
    {
      key: 'data.props.items',
      label: '天时分项配置',
      component: 'TimeRangeStatsItemsInput',
      fieldProps: {},
    },
  ],
  [
    {
      key: 'data.props.hourOnlyItems',
      label: '仅输入小时配置',
      component: 'TimeRangeStatsItemsInput',
      dependencyKey: 'data.props.hourOnly',
      dependencyValue: 'true',
      fieldProps: {},
    },
  ],
];

// TODO 费用配置
export const FeeItemProperties: PropertyItem[][] = [
  [
    {
      key: 'data.props.prefix',
      label: '费用名称',
      component: 'Input',
      fieldProps: {},
    },
  ],
  [
    {
      key: '',
      label: '费用项',
      component: 'FeeItemsInput',
      fieldProps: {},
    },
  ],
];

// TODO 婴儿年龄配置
export const BabyAgeProperties: PropertyItem[][] = [];

export const PostCodeProperties: PropertyItem[][] = [
  [
    {
      key: 'data.props.codeLength',
      label: '字段长度',
      width: 'xs',
      component: 'Digit',
      fieldProps: {
        precision: 0,
        min: 1,
      },
    },
  ],
  [
    {
      key: 'data.props.disabled',
      label: '禁用',
      width: 'xs',
      component: 'Switch',
      fieldProps: {},
    },
  ],
];

export const LabelProperties: PropertyItem[][] = [
  [
    {
      key: 'data.props.label',
      label: '需要显示的文案',
      width: 'lg',
      component: 'Input',
    },
  ],
  [
    {
      key: 'data.props.dictionaryModuleGroup',
      label: '字典库组',
      width: 'xs',
      component: 'Select',
      valueEnum: PropertyItemConstants.ModelDataGroupOptions,
      fieldProps: {},
    },

    {
      key: 'data.props.dictionaryModuleKey',
      label: '字典库',
      width: 'xs',
      component: 'Input',
      fieldProps: {},
    },
  ],
  [
    {
      key: 'data.props.dictionaryItemLabelFormat',
      label: '字典库项显示格式',
      description: '例：{Code} {Name} / {index} {Code} {Name}',
      width: 'md',
      component: 'Input',
    },
  ],
];

export const InHospitalOthersProperties: PropertyItem[][] = [
  ...UniSelectProperties,
  [
    {
      key: 'data.props.hospInputType',
      label: '机构输入方式',
      width: 'lg',
      component: 'HospInutType',
    },
  ],
];

export const OutTypeProperties: PropertyItem[][] = [
  ...UniSelectProperties,
  [
    {
      key: 'data.props.labelMapping',
      label: '离院方式文案',
      width: 'lg',
      component: 'OutTypeLabelMappingInput',
    },
  ],
  [
    {
      key: 'data.props.hospInputType',
      label: '机构输入方式',
      width: 'lg',
      component: 'HospInutType',
    },
  ],
];

export const SectionHeaderProperties: PropertyItem[][] = [
  [
    {
      key: 'data.props.hideInDmr',
      label: '首页隐藏',
      width: 'xs',
      component: 'Switch',
    },
    {
      key: 'data.props.label',
      label: 'Section标题',
      width: 'md',
      component: 'Input',
    },
    {
      key: 'data.props.sectionHeaderFontColor',
      label: 'Section标题颜色',
      width: 'md',
      component: 'ColorSelector',
    },
  ],
  [
    {
      key: 'data.props.sectionHeaderBackgroundColor',
      label: '背景颜色',
      width: 'md',
      component: 'ColorSelector',
    },
    {
      key: 'data.props.sectionHeaderBorderColor',
      label: '头部边框颜色',
      width: 'md',
      component: 'ColorSelector',
    },

    {
      key: 'data.props.sectionBottomBorderColor',
      label: '底部边框颜色',
      width: 'md',
      component: 'ColorSelector',
    },
  ],
];

export const SectionBottomProperties: PropertyItem[][] = [
  [
    {
      key: 'data.props.hideInDmr',
      label: '首页隐藏',
      width: 'xs',
      component: 'Switch',
    },
    {
      key: 'data.props.sectionBottomBorderColor',
      label: '边框颜色',
      width: 'md',
      component: 'ColorSelector',
    },
  ],
];
