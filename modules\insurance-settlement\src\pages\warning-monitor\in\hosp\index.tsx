import { Reducer, useEffect, useMemo, useReducer, useState } from 'react';
import { Col, Drawer, Row, TableProps } from 'antd';
import { RespVO } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import _ from 'lodash';
import { useModel } from '@@/plugin-model/useModel';
import { IReducer, ITableState } from '@uni/reducers/src/interface';
import { InitTableState, TableAction, tableReducer } from '@uni/reducers/src';
import './index.less';
import { useLocation, useRequest } from 'umi';
import SingleStat from '@uni/components/src/statistic';
import { IWarningStats } from '../../interface';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import TreeCtrlTable from '@uni/components/src/tree-ctrl-table';
import { WarningLevelOpts } from '../../constants';
import UniEcharts from '@uni/components/src/echarts';
import { hospDeptWarningCntPie } from '../../chat.opts';
import IconBtn from '@uni/components/src/iconBtn/index';
import DrawerCardInfo from '@/pages/dip/components/drawerCardInfo/index';
import PatientChsDetail from '../../patientInfo/index';
import { isEmptyValues } from '@uni/utils/src/utils';

const WarningMonitorHosp = () => {
  const {
    globalState: { dictData, searchParams, userInfo },
  } = useModel('@@qiankunStateFromMaster');
  const location = useLocation();

  // 记录一份tableParams
  const [tableParams, setTableParams] = useState(undefined);

  // drawer
  // const [drawerVisible, setDrawerVisible] = useState(undefined);
  // 单独对cardList的点击drawer state
  const [cardListDrawerState, setCardListDrawerState] = useState({
    visible: false,
    hisId: undefined,
  });

  // 主table
  const [TableState, TableDispatch] = useReducer<
    Reducer<ITableState<any> & { fetchParams: any }, IReducer>
  >(tableReducer, { ...InitTableState, fetchParams: undefined });

  const [abnormalStatsOpts, setAbnormalStatsOpts] = useState([]);
  const [warningStatsOpts, setWarningStatsOpts] = useState([]);

  const [allCardData, setAllCardData] = useState<any>();
  // stats part 1
  const {
    data: stats,
    loading: statsFetchLoading,
    run: statsFetch,
  } = useRequest(
    (params) =>
      uniCommonService(
        `Api/Emr/${
          location.pathname.includes('dip')
            ? 'EmrDipWarningStats'
            : 'EmrDrgWarningStats'
        }/WarningStatsOfCliDept`,
        {
          method: 'POST',
          data: params,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<IWarningStats[]>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 预警人次bar opts
          // setWarningStatsOpts(res.data?.at(0));
          setAllCardData(res.data?.at(0) || {});
          return res.data?.at(0);
        }
        return null;
      },
    },
  );
  // stats part 2
  const {
    data: todayStats,
    loading: todayStatsFetchLoading,
    run: todayStatsFetch,
  } = useRequest(
    (params) =>
      uniCommonService(
        `Api/Emr/${
          location.pathname.includes('dip')
            ? 'EmrDipWarningStats'
            : 'EmrDrgWarningStats'
        }/TodayStats`,
        {
          method: 'POST',
          data: params,
        },
      ),
    {
      manual: true,
      formatResult: (res: RespVO<any[]>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data?.at(0);
        }
        return null;
      },
    },
  );

  // tree-table
  // tree part
  const {
    data: treePart,
    loading: treePartFetchLoading,
    run: treePartFetch,
  } = useRequest(
    (params) =>
      uniCommonService('Api/Emr/EmrSettleCheckWarning/GetSettleCheckStats', {
        method: 'POST',
        data: params,
      }),
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          // 这里处理数据 固定层数2 先写死
          let { data } = res,
            treeData = [];
          if (data?.WarningLevelStats?.length > 0) {
            treeData = _.orderBy(
              data?.WarningLevelStats,
              ['WarningLevel'],
              ['desc'],
            )?.map((d, index) => {
              if (d?.RuleStats && d?.RuleStats?.length > 0) {
                let children = [];
                children = _.orderBy(d?.RuleStats, ['Sort'])?.map((v, i) => ({
                  key: `key-${index}-${i}`,
                  title: v.DisplayErrMsg,
                  parentTitle: WarningLevelOpts?.[d.WarningLevel],
                  name: v.DisplayErrMsg,
                  value: v.PatCnt,
                  remark: v?.Remark,
                  Sort: v?.Sort + 1,
                  args: {
                    RuleCode: v.RuleCode,
                    WarningLevel: d.WarningLevel,
                  },
                }));
                return {
                  key: `key-${index}`,
                  title: WarningLevelOpts?.[d.WarningLevel],
                  name: WarningLevelOpts?.[d.WarningLevel],
                  value: d.PatCnt,
                  args: { WarningLevel: d.WarningLevel },
                  children,
                };
              }
              return {
                key: `key-${index}`,
                title: WarningLevelOpts?.[d.WarningLevel],
                name: WarningLevelOpts?.[d.WarningLevel],
                value: d.PatCnt,
                args: { WarningLevel: d.WarningLevel },
              };
            });
          }

          const allValue =
            allCardData?.SupervisePatCnt ?? stats?.SupervisePatCnt ?? 0;
          const IndicativeValue = data.IndicativeWarningLevelPatCnt ?? 0;
          const NormalValue = allValue - (data?.ProblematicCardCnt ?? 0);

          treeData.unshift({
            key: 'key' + 'Indicative',
            name: '指示性数据',
            title: '指示性数据',
            value: IndicativeValue ?? 0,
            args: {
              WarningLevel: 'Indicative',
            },
          });

          const allTreeData = [
            {
              key: 'key' + 'Normal',
              name: '正常病例数',
              title: '正常病例数',
              value: NormalValue ?? 0,
              args: {
                WarningLevel: 'Normal',
              },
            },
            {
              key: 'key-problem',
              name: '问题病例数',
              title: '问题病例数',
              disabled: true,
              value: data?.ProblematicCardCnt ?? 0,
              children: treeData,
            },
          ];

          setWarningStatsOpts([
            {
              name: '黄色异常',
              data: 'YellowWarningLevelPatCnt',
              value: data?.YellowWarningLevelPatCnt || 0,
              itemStyle: {
                color: '#f5d06c',
              },
              totalFee: data?.YellowWarningLevelTotalFee || 0,
            },
            {
              name: '橙色异常',
              data: 'OrangeWarningLevelPatCnt',
              value: data?.OrangeWarningLevelPatCnt || 0,
              itemStyle: {
                color: '#f59c6c',
              },
              totalFee: data?.OrangeWarningLevelTotalFee || 0,
            },
            {
              name: '红色异常',
              data: 'RedWarningLevelPatCnt',
              value: data?.RedWarningLevelPatCnt || 0,
              itemStyle: {
                color: '#f56c6c',
              },
              totalFee: data?.RedWarningLevelTotalFee || 0,
            },
          ]);
          // 最后用 全部 将 数据包裹
          treeData = [
            {
              key: 'key-all',
              title: '全部',
              name: '全部',
              value: allValue,
              children: allTreeData,
            },
          ];

          return { ...res.data, treeDataResult: treeData };
        }
        setWarningStatsOpts([]);
        return { treeDataResult: [] };
      },
    },
  );
  // table-part:columns
  const { loading: tableColumnsFetchLoading, run: tableColumnsFetch } =
    useRequest(
      () =>
        uniCommonService(
          'Api/Emr/EmrSettleCheckWarning/GetSettleCheckCardDetails',
          {
            method: 'POST',
            headers: {
              'Retrieve-Column-Definitions': 1,
            },
          },
        ),
      {
        formatResult: (res) => {
          if (res?.code === 0 && res?.statusCode === 200) {
            TableDispatch({
              type: TableAction.columnsChange,
              payload: {
                columns: tableColumnBaseProcessor([], res?.data?.Columns),
              },
            });
          }
          return null;
        },
      },
    );
  // table-part:datasource
  const {
    data: tablePart,
    loading: tablePartFetchLoading,
    run: tablePartFetch,
  } = useRequest(
    (data, pagi, sorter = null) => {
      let finalData = {
        DtParam: {
          Draw: 1,
          Start: (pagi.cur - 1) * pagi.size,
          Length: pagi.size,
        },
        ...data,
        ruleCode: data?.ruleCode,
      };
      return uniCommonService(
        'Api/Emr/EmrSettleCheckWarning/GetSettleCheckCardDetails',
        {
          method: 'POST',
          data: finalData,
        },
      );
    },
    {
      manual: true,
      formatResult: (res) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          return res.data;
        }
        return null;
      },
      onSuccess: (data, params) => {
        if (data) {
          // data + pagi
          TableDispatch({
            type: TableAction.dataPagiChange,
            payload: {
              data: data?.data ?? [],
              backPagination: {
                ...TableState.backPagination,
                current: params?.at(1)?.cur,
                pageSize: params?.at(1)?.size,
                total: data?.recordsFiltered ?? 0,
              },
            },
          });
          // sorter
          if (!_.isEqual(params?.at(2), TableState.sorter)) {
            TableDispatch({
              type: TableAction.sortChange,
              payload: { sorter: params?.at(2) },
            });
          }
        }
      },
    },
  );

  // 处理树选中刷新table
  const handleTreeOnSelect = (keys: React.Key[], info: any) => {
    let { node } = info;
    TableDispatch({
      type: TableAction.anyChange, // 把fetchParams当作接口的部分参数记录，只记录要记的，比如searchParams的就别记
      payload: {
        title: node?.title,
        fetchParams: {
          ruleCode: node?.selected ? undefined : node?.args?.RuleCode,
          WarningLevel: node?.selected ? undefined : node?.args?.WarningLevel,
        },
      },
    });
    tablePartFetch(
      {
        ...tableParams,
        ruleCode: node?.selected ? undefined : node?.args?.RuleCode,
        WarningLevel: node?.selected ? undefined : node?.args?.WarningLevel,
      },
      {
        cur: 1,
        size:
          TableState.backPagination?.pageSize ??
          TableState.backPagination?.defaultPageSize,
      },
    );
  };

  const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    // setBackPagination({
    //   ...backPagination,
    //   current: pagi.current,
    //   pageSize: pagi.pageSize,
    // });
    tablePartFetch(
      {
        ...tableParams,
        ...(TableState?.fetchParams || {}),
      },
      { cur: pagi.current, size: pagi.pageSize },
    );
  };

  const init = async (tableParams) => {
    await statsFetch(tableParams);
    await todayStatsFetch(tableParams);
    await treePartFetch(tableParams);
  };

  useEffect(() => {
    if (
      (searchParams && searchParams?.triggerSource === 'btnClick') ||
      (isEmptyValues(tableParams) && searchParams?.hospCodes?.length)
    ) {
      let tableParams = {
        HospCode: searchParams?.hospCodes,
        // drg dip 要做区分
        // CliDepts:
        //   searchParams?.CliDepts?.length > 0
        //     ? searchParams?.CliDepts
        //     : userInfo?.CliDepts,
        Wards:
          searchParams?.wards?.length > 0
            ? searchParams?.wards
            : userInfo?.Wards,
      };
      setTableParams(tableParams);
      init(tableParams);
      tablePartFetch(
        {
          ...tableParams,
          ...(TableState?.fetchParams || {}),
        },
        {
          cur: TableState.backPagination?.current,
          size:
            TableState.backPagination?.pageSize ??
            TableState.backPagination?.defaultPageSize,
        },
      );
    }
  }, [
    searchParams?.hospCodes,
    searchParams?.CliDepts,
    searchParams?.wards,
    userInfo,
  ]);

  // 异常人数chart
  useEffect(() => {
    if (stats && treePart?.RuleStats) {
      setAbnormalStatsOpts([
        {
          name: '低倍率',
          key: 'LowPatCnt',
          value: stats?.LowPatCnt,
        },
        {
          name: '高倍率',
          key: 'HighPatCnt',
          value: stats?.HighPatCnt,
        },
        ...treePart?.RuleStats?.map((d) => ({
          name: d.DisplayErrMsg,
          key: d.RuleCode,
          value: d.PatCnt,
        })),
      ]);
    }
  }, [stats, treePart]);

  // columns
  const renderColumns = useMemo(() => {
    if (TableState.columns?.length > 0) {
      return [
        {
          dataIndex: 'operation',
          visible: true,
          width: 40,
          align: 'center',
          fixed: 'left',
          title: '',
          order: 1,
          render: (node, record, index) => {
            return (
              <IconBtn
                title="查看病案首页"
                type="details2"
                onClick={(e) => {
                  if (!record?.HisId) return;
                  setCardListDrawerState({
                    visible: true,
                    hisId: record?.HisId,
                  });
                  // setDrawerVisible({
                  //   hisId: record?.HisId,
                  //   type: location.pathname.includes('dip') ? 'dip' : 'drg',
                  // });
                }}
              />
            );
          },
        },
        ...TableState.columns,
      ];
    }
    return [];
  }, [TableState.columns]);

  return (
    <>
      <Row gutter={[8, 8]} style={{ marginBottom: '8px' }}>
        <Col span={7}>
          <SingleStat
            className="warning_stats"
            loading={statsFetchLoading}
            title="在院病人"
            suffix="人"
            value={stats?.SupervisePatCnt ?? 0}
            rightFooterTitle={
              location.pathname.includes('dip') ? 'DIP结算人次' : 'DRG结算人次'
            }
            rightFootValue={stats?.ApplicablePatCnt ?? 0}
            footerTitle="今日入院人次"
            footerValue={todayStats?.TodayInPatCnt ?? 0}
          />
        </Col>
        <Col span={7}>
          <SingleStat
            className="warning_stats"
            title="盈亏"
            dataType="Currency"
            loading={statsFetchLoading}
            suffix="元"
            prefix="￥"
            value={stats?.Profit ?? 0}
            footerTitle="总费用"
            footerValue={stats?.TotalFee ?? 0}
            rightFooterTitle="高倍率人次"
            rightFootValue={stats?.HighPatCnt ?? 0}
          />
        </Col>
        <Col span={10}>
          <SingleStat
            className="warning_chart_stats"
            title="异常等级"
            noValue={true}
            loading={statsFetchLoading}
            prefix={
              <UniEcharts
                height={133}
                width={'100%'}
                elementId="warning_cnt_bar"
                options={
                  (stats && hospDeptWarningCntPie(warningStatsOpts)) || {}
                }
              />
            }
          />
        </Col>
      </Row>

      {/* 底部的tree+table */}
      <TreeCtrlTable
        treeData={{
          title: '异常类型',
          subTitle: '问题病例数',
          subKey: 'ProblematicCardCnt',
          data: {
            ...treePart,
          },
        }}
        defaultExpandedKeys={['key-problem']}
        treeLoading={treePartFetchLoading}
        treeAction={{
          // onSearch: (e) => {
          //   //TODO
          //   console.log(e.target.value);
          // },
          onSelect: handleTreeOnSelect,
        }}
        tableData={{
          columns: renderColumns,
          dataSource: TableState.data,
          rowKey: 'Id',
          id: 'warning_hosp_table',
          loading: tablePartFetchLoading,
          pagination: TableState.backPagination,
          onChange: backTableOnChange,
          dictionaryData: dictData,
          scroll: { x: 'max-content' },
          // 3个额外的
          tableTitle: TableState.title,
          export: {
            isBackend: true,
            backendObj: {
              url: 'Api/Emr/EmrSettleCheckWarning/ExportGetSettleCheckCardDetails',
              method: 'POST',
              data: {
                ...tableParams,
                ...(TableState?.fetchParams || {}),
              },
              fileName: TableState.title as string,
            },
            btnDisabled: TableState.data?.length < 1,
          },
          columnEdit: {
            columnInterfaceUrl:
              'Api/Emr/EmrSettleCheckWarning/GetSettleCheckCardDetails',
            onTableRowSaveSuccess: (columns) => {
              TableDispatch({
                type: TableAction.columnsChange,
                payload: {
                  columns: tableColumnBaseProcessor([], columns),
                },
              });
            },
          },
        }}
      />
      {/* <DrawerCardInfo
        visible={drawerVisible}
        onClose={() => setDrawerVisible(undefined)}
        isEmr={true}
      /> */}
      {/* 单独的drawer 对应card点击 */}
      <Drawer
        open={cardListDrawerState?.visible}
        width={'100%'}
        destroyOnClose={true}
        onClose={() => {
          setCardListDrawerState({
            visible: false,
            hisId: undefined,
          });
        }}
        maskClosable={false}
        title="明细"
      >
        <PatientChsDetail hisId={cardListDrawerState?.hisId} />
      </Drawer>
    </>
  );
};

export default WarningMonitorHosp;
