import { Button, message, Modal, List, Form } from 'antd';
import { uniCommonService } from '@uni/services/src';
import { useModel } from '@@/plugin-model/useModel';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import { RespVO } from '@uni/commons/src/interfaces';
import { isEmptyValues } from '@uni/utils/src/utils';
import {
  UploadOutlined,
  CloseCircleTwoTone,
  CheckCircleTwoTone,
  SyncOutlined,
} from '@ant-design/icons';
import { useState } from 'react';
import { ModalForm, ProFormDateRangePicker } from '@ant-design/pro-components';
import { UniSelect } from '@uni/components/src/index';

export default function InterfaceImportBtn({
  type,
  refreshTable,
  disabled,
}: {
  type: 'byDay' | 'byDept';
  refreshTable: () => void;
  disabled?: boolean;
}) {
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { searchParams } = globalState;
  const [form] = Form.useForm<{}>();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [taskId, setTaskId] = useState<number | null>(null);
  const [importData, setImportData] = useState<any>([]);
  const [params, setParams] = useState<any>({});

  const { run: getTriggerUdfConfigsReq } = useRequest(
    () =>
      uniCommonService(`Api/Sys/UdfConfigSys/GetTriggerUdfConfigs`, {
        method: 'POST',
        data: {
          trigger: 'PullInpatientAmtCommand',
        },
      }),
    {
      manual: true,
      onSuccess: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          // 保存TaskId用于轮询
          setTaskId(+response?.data?.[0]?.UdfScript);
          getUdfExecuteReq(+response?.data?.[0]?.Id);
        }
      },
    },
  );
  const { run: getUdfExecuteReq } = useRequest(
    (id) =>
      uniCommonService(`Api/Udf/Udf/ExecuteInBackground/${id}`, {
        method: 'POST',
        data: {
          ...params,
        },
      }),
    {
      manual: true,
      onSuccess: (res: RespVO<any>) => {
        if (res?.code === 0 && res?.statusCode === 200) {
          message.info('成功创建导入任务');
          InterfaceImport();
        }
      },
    },
  );
  const { run: InterfaceImport, cancel: cancelPolling } = useRequest(
    () => {
      const requestParams = {
        ...params,
        TaskId: taskId,
      };

      return uniCommonService('Api/Udf/InterfaceSetCmd/GetRecentTaskMasters', {
        method: 'POST',
        data: requestParams,
      });
    },
    {
      manual: true,
      pollingInterval: 2000, // 每2秒轮询一次
      onSuccess: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          const tasks = response.data || [];
          setImportData(tasks);
          setIsModalOpen(true);

          // 检查所有任务是否都完成 (TaskRecordStatus 为 999 或 100)
          const allCompleted =
            tasks.length > 0 &&
            tasks.every(
              (task: any) =>
                task.TaskRecordStatus === '999' ||
                task.TaskRecordStatus === '100',
            );

          if (allCompleted) {
            message.success('导入完成');
            cancelPolling();
          }
        } else {
          // 如果接口调用失败，也停止轮询
          message.error('获取任务状态失败');
          cancelPolling();
        }
      },
    },
  );

  const handleCancel = () => {
    setIsModalOpen(false);
    cancelPolling();
    refreshTable();
  };

  return (
    <>
      <ModalForm
        title="接口导入"
        width={600}
        trigger={
          <Button key="InterfaceImport" type="text" icon={<UploadOutlined />}>
            接口导入
          </Button>
        }
        form={form}
        autoFocusFirstInput
        layout="horizontal"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        modalProps={{
          destroyOnClose: true,
          maskClosable: false,
        }}
        initialValues={{
          dateRange:
            type === 'byDay'
              ? [searchParams?.singleDate, searchParams?.singleDate]
              : searchParams?.dateRange,
          HospCode: searchParams?.hospCodes,
          DeptCode: type === 'byDept' ? [searchParams.dynDept] : undefined,
        }}
        submitTimeout={2000}
        onFinish={async (values) => {
          const newValues = {
            ...values,
            Sdate: values?.dateRange?.at(0),
            Edate: values?.dateRange?.at(1),
          };
          delete newValues.dateRange;
          setParams(newValues);
          getTriggerUdfConfigsReq();
          return true;
        }}
      >
        <ProFormDateRangePicker
          label="时间"
          name="dateRange"
          allowClear={false}
          fieldProps={{
            style: { width: '100%' },
          }}
          rules={[
            {
              required: true,
              message: '请选择时间',
            },
          ]}
        />
        <Form.Item name="HospCode" label="院区">
          <UniSelect
            showSearch
            dataSource={globalState?.dictData?.Hospital ?? []}
            mode="multiple"
            optionNameKey="Name"
            optionValueKey="Code"
            placeholder={'请选择'}
            maxTagCount="responsive"
          />
        </Form.Item>
        <Form.Item name="DeptCode" label="科室">
          <UniSelect
            showSearch
            dataSource={globalState?.dictData?.CliDepts ?? []}
            mode="multiple"
            optionNameKey="Name"
            optionValueKey="Code"
            placeholder={'请选择'}
            maxTagCount="responsive"
          />
        </Form.Item>
      </ModalForm>

      {isModalOpen && (
        <Modal
          title="导入任务"
          open={isModalOpen}
          onOk={handleCancel}
          onCancel={handleCancel}
          maskClosable={false}
          footer={null}
          bodyStyle={{
            overflowY: 'auto',
            height: 480,
          }}
        >
          <List
            itemLayout="horizontal"
            dataSource={importData}
            renderItem={(item: {
              TaskRecordStatus: string;
              TaskRecordStatusName: string;
              Sdate: string;
              Edate: string;
            }) => (
              <List.Item>
                <List.Item.Meta
                  avatar={
                    item.TaskRecordStatus === '999' ? (
                      <CloseCircleTwoTone twoToneColor={'#eb5757'} />
                    ) : item.TaskRecordStatus === '100' ? (
                      <CheckCircleTwoTone twoToneColor={'#18ba56'} />
                    ) : (
                      <SyncOutlined style={{ color: '#1464f8' }} spin />
                    )
                  }
                  title={item.TaskRecordStatusName}
                  description={
                    <span>
                      {dayjs(item?.Sdate).format('YYYY-MM-DD')}
                      <span style={{ margin: '0 8px' }}>-</span>
                      {dayjs(item?.Edate).format('YYYY-MM-DD')}
                    </span>
                  }
                />
              </List.Item>
            )}
          />
        </Modal>
      )}
    </>
  );
}
