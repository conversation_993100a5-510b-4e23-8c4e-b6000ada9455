import { icdeDictColumns } from '@/pages/configuration/base/columns';
import {
  Button,
  Card,
  Form,
  Input,
  Modal,
  Space,
  TableProps,
  message,
  Checkbox,
  Select,
  Divider,
} from 'antd';
import React, {
  Fragment,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useRequest } from 'umi';
import { RespVO, TableColumns, TableResp } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { IcdeDictionaryItem } from '@/pages/configuration/base/interfaces';
import './index.less';
import IcdeDictionaryAdd from './components/add';
import {
  icdeAddEditRequestProcessor,
  icdeTypesProcessor,
} from '@/pages/configuration/base/icde/processor';
import UniEditableTable from '@uni/components/src/table/edittable';
import { v4 as uuidv4 } from 'uuid';
import {
  useDebounce,
  useDebounceFn,
  useMutationObserver,
  useThrottleFn,
} from 'ahooks';
import ConfigExcelTemplateHandler from '@/components/configExcelTemplateHandler';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { useUpdateEffect } from 'ahooks';
import DebounceSelect from '@/components/debounceSelect';
import { UniTable } from '@uni/components/src';
import ISD from '@uni/components/src/infinite-scroll';
import _ from 'lodash';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';

interface IcdeDictionaryProps {
  moduleGroup?: string;
}
const Event = 'DmrIcde';

const InfiniteScrollId = 'icde-isd';

const IcdeDictionary = (props?: IcdeDictionaryProps) => {
  const [form] = Form.useForm();
  const ref = useRef<any>();

  // keywords
  const [searchKeywords, setSearchKeywords] = useState('');
  const debouncedKeywords = useDebounce(searchKeywords, { wait: 1000 });

  const [icdeDictionaryAdd, setIcdeDictionaryAdd] = useState(false);

  // const [icdeDictionaryTableDataSource, setIcdeDictionaryTableDataSource] =
  //   useState([]);

  const [icdeDictionaryColumns, setIcdeDictionaryColumns] = useState([]);

  const [backPagination, setBackPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
    hideOnSinglePage: false,
  });

  const [pagiStart, setPagiStart] = useState(1);
  // 记录icdekeyword
  const [icdeKeyword, setIcdeKeyword] = useState(undefined);

  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  // 几个infinite新值
  const infiniteChildRef = useRef<any>();
  const [infiniteData, setInfiniteData] = useState([]);
  const [infiniteLoading, setInfiniteLoading] = useState(false);
  const [editIndex, setEditIndex] = useState(-1);

  useEffect(() => {
    icdeConfigurationColumnsReq();
  }, []);

  // useEffect(() => {
  //   icdeDictionaryReq(
  //     backPagination?.current || 1,
  //     backPagination?.pageSize || 10,
  //   );
  // }, [debouncedKeywords]);

  // const backTableOnChange: TableProps<any>['onChange'] = (pagi) => {
  //   setBackPagination({
  //     ...backPagination,
  //     current: pagi.current,
  //     pageSize: pagi.pageSize,
  //   });

  //   icdeDictionaryReq(pagi.current, pagi.pageSize);
  // };

  // handle dmrCardsSummarySelectedKey （上面4个summary切换key）
  const dmrCardsSummarySelectedKeyHandler = (data) => {
    data['IsDamg'] = checkNick ? checkNick : undefined;
    return data;
  };

  const { run: icdeConfigurationColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Sys/CodeSys/GetDmrIcdeWithAllCompares', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setIcdeDictionaryColumns(
            tableColumnBaseProcessor(
              icdeDictColumns(Event),
              response?.data?.Columns,
            ),
          );
        } else {
          setIcdeDictionaryColumns([]);
        }
      },
    },
  );

  const { loading: icdeUpsertLoading, run: icdeUpsertReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        ...values,
        // ...icdeAddEditRequestProcessor(values),
        ModuleGroup: props?.moduleGroup,
      };

      return uniCommonService('Api/Sys/CodeSys/UpsertDmrIcdeAllCompare', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.statusCode === 200) {
          setIcdeDictionaryAdd(false);
          // TODO 给编辑的位置
          // icdeDictionaryReq(
          //   infiniteScrollPagi.Start,
          //   infiniteScrollPagi.Length,
          // );
          return response?.data;
        }
      },
      onSuccess: (data, ret) => {
        // 现在这边hack 因为无限滚动 不能再重新获取数据 需要使用记录的index与编辑的结果
        // 最好的是upsert该接口返回后端结果，确保数据正确
        if (editIndex > -1) {
          setInfiniteData(
            infiniteData?.map((d, i) => {
              return d?.IcdeId === data ? { ...d, ...ret?.at(0) } : d;
            }),
          );
          setEditIndex(-1); // reset
        } else if (editIndex === -1 && data) {
          // 新增的情况 之后讨论下怎么进行数据处理 应该是后端返回这个新增的当前位置？TODO
          Emitter.emit(EventConstant.INFINITE_SCROLL_RELOAD_FETCH);
        }
      },
    },
  );

  const { run: icdeDeleteReq } = useRequest(
    (values) => {
      let data = {};
      data = {
        ModuleGroup: props?.moduleGroup,
        icdeId: values.IcdeId,
      };
      return uniCommonService('Api/Sys/CodeSys/DeleteDmrIcde', {
        method: 'POST',
        data: data,
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<number>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('删除成功');
          return response?.data;
        }
      },
      onSuccess: (data, params) => {
        // TODO 给编辑的位置
        // icdeDictionaryReq(backPagination?.current, backPagination?.pageSize);
        if (data) {
          setInfiniteData(
            infiniteData?.filter(
              (d, i) => d?.TcmIcdeId !== params?.at(0)?.TcmIcdeId,
            ),
          );
        }
      },
    },
  );

  // 编辑 & 新增 req
  const onIcdeDictionaryItemAdd = (values: any) => {
    icdeUpsertReq(
      editIndex > -1
        ? {
            ...infiniteData?.find((d, i) => i === editIndex),
            ...values,
          }
        : values,
    );
  };

  useEffect(() => {
    Emitter.onMultiple(
      [
        `${ConfigurationEvents.DMR_INSURANCE_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_HQMS_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_WT_ICDE_SELECT}#${Event}`,
      ],
      (data) => {
        let currentFormValue = form.getFieldsValue()?.[data?.id];
        if (currentFormValue) {
          // set form value
          Object.keys(data?.values)?.forEach((key) => {
            currentFormValue[key] = data?.values?.[key];
          });
          form.setFieldValue(data?.id, currentFormValue);
        }
      },
    );

    Emitter.on(ConfigurationEvents.DMR_ICDE_EDIT, (data) => {
      if (data?.index > -1) {
        if (data?.record?.IcdeId) {
          // index 要记录 会使用到
          form.setFieldsValue(data.record);
          setEditIndex(data?.index);
          setIcdeDictionaryAdd(true);
        }
      }
    });

    Emitter.on(ConfigurationEvents.DMR_ICDE_DELETE, (data) => {
      if (data?.index > -1) {
        icdeDeleteReq(data.record);
      }
    });

    return () => {
      Emitter.offMultiple([
        `${ConfigurationEvents.DMR_INSURANCE_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_HQMS_ICDE_SELECT}#${Event}`,
        `${ConfigurationEvents.DMR_WT_ICDE_SELECT}#${Event}`,
      ]);
      Emitter.off(ConfigurationEvents.DMR_ICDE_EDIT);
      Emitter.off(ConfigurationEvents.DMR_ICDE_DELETE);
    };
  }, [infiniteData]);

  const [checkNick, setCheckNick] = useState(false);
  const onCheckboxChange = (e: { target: { checked: boolean } }) => {
    setCheckNick(e.target.checked);
  };

  // debounce select
  async function fetchIcdeSelect(keyword: string): Promise<any[]> {
    if (!keyword) return;
    return await uniCommonService('Api/Dmr/DmrSearch/Icde', {
      params: {
        // IsDamg: true,
        KeyWord: keyword,
        SkipCount: 0,
        MaxResultCount: 100,
      },
    });
  }

  // search emitter
  useEffect(() => {
    const req = async ({ searchedValue, reqData, cb }) => {
      console.log(searchedValue, checkNick);
      let response = await uniCommonService(
        'Api/Sys/CodeSys/SearchDmrIcdeWithAllCompare',
        {
          method: 'POST',
          data: {
            code: searchedValue,
            IsDamg: reqData?.IsDamg,
          },
        },
      );
      if (response?.code === 0) {
        cb(response.data);
      }
    };

    Emitter.on(
      EventConstant.INFINITE_SCROLL_CB_FETCH,
      ({ searchedValue, reqData, cb, isdId }) => {
        if (isdId === InfiniteScrollId) {
          req({ searchedValue, reqData, cb });
        }
      },
    );

    return () => {
      Emitter.off(EventConstant.INFINITE_SCROLL_CB_FETCH);
    };
  }, []);

  return (
    <>
      <Card
        title="诊断列表"
        extra={
          <Space>
            <ConfigExcelTemplateHandler
              downloadTemplateApiObj={{
                apiUrl: 'Api/Sys/CodeSys/GetDmrIcdeAllCompareExcelTemplate',
              }}
              downloadPostData={{
                moduleGroup: props?.moduleGroup,
                exportName: '西医诊断列表',
              }}
              uploadXlsxApiObj={{
                apiUrl: 'Api/Sys/CodeSys/UploadDmrIcdeAllCompareExcelFile',
                onSuccess: () => {
                  Emitter.emit(EventConstant.INFINITE_SCROLL_RELOAD_FETCH);

                  // TODO 当前位置
                  // icdeDictionaryReq(
                  //   backPagination?.current,
                  //   backPagination?.pageSize,
                  // );
                },
              }}
              uploadPostData={{
                moduleGroup: props?.moduleGroup,
              }}
            />
            <Button
              key="add"
              loading={false}
              onClick={(e) => {
                // ref.current?.addEditRecord({
                //   id: uuidv4()
                // });
                setIcdeDictionaryAdd(true);
                form.setFieldValue('IsValid', true);
                form.setFieldValue('IsMain', true);
              }}
            >
              新增诊断
            </Button>
            <Divider type="vertical" />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl: 'Api/Sys/CodeSys/GetDmrIcdeWithAllCompares',
                onTableRowSaveSuccess: (columns) => {
                  setIcdeDictionaryColumns(
                    tableColumnBaseProcessor(icdeDictColumns(Event), columns),
                  );
                },
              }}
            />
          </Space>
        }
      >
        <UniTable
          actionRef={ref}
          id={`icde-dictionary-table`}
          className={'icde-dictionary-table'}
          rowKey={'uuidv4'}
          scroll={{
            y: 480,
            x: 'max-content',
          }}
          headerTitle={
            <DebounceSelect
              value={icdeKeyword}
              placeholder="请输入诊断编码或名称"
              fetchOptions={fetchIcdeSelect}
              onChange={(newValue) => {
                setIcdeKeyword(newValue);
              }}
              fieldNames={{
                label: 'Name',
                value: 'Code',
              }}
              style={{ width: '350px', marginBottom: '10px' }}
            />
          }
          toolBarRender={() => [
            <div>
              <Checkbox checked={checkNick} onChange={onCheckboxChange}>
                筛选损伤中毒编码
              </Checkbox>
            </div>,
          ]}
          // backendPagination
          bordered={true}
          loading={infiniteLoading || icdeUpsertLoading}
          columns={icdeDictionaryColumns}
          dataSource={infiniteData}
          clickable={false}
          pagination={false}
        />
      </Card>

      <Modal
        title={editIndex > -1 ? '编辑诊断' : '新增诊断'}
        open={icdeDictionaryAdd}
        onOk={() => {
          onIcdeDictionaryItemAdd(form.getFieldsValue());
        }}
        onCancel={() => {
          setIcdeDictionaryAdd(false);
        }}
        destroyOnClose={true}
        getContainer={document.getElementById('icde-configuration-container')}
        okButtonProps={{
          htmlType: 'submit',
        }}
      >
        <IcdeDictionaryAdd form={form} event={Event} />
      </Modal>

      <ISD
        id={InfiniteScrollId}
        scrollFetchObj={{
          url: 'Api/Sys/CodeSys/GetDmrIcdeWithAllCompares',
        }}
        scrollDom={document?.querySelector(
          `div[id='icde-dictionary-table'] div[class=ant-table-body]`,
        )}
        reqData={{
          Code: icdeKeyword,
          IsDamg: checkNick ? checkNick : undefined,
        }}
        dataSource={infiniteData}
        setDataSource={setInfiniteData}
        setLoading={setInfiniteLoading}
        searchedValue={icdeKeyword}
        searchedKey="Code"
      />
    </>
  );
};

export default IcdeDictionary;
