import {
  <PERSON>Form,
  <PERSON>Form<PERSON>ext,
  ProFormSelect,
  DrawerForm,
  ProFormDigit,
  ProFormSwitch,
} from '@uni/components/src/pro-form';
import { ComponentItem } from '@/pages/configuration/interfaces';
import {
  AutoSelectProperties,
  DmrSelectProperties,
  IcdeSelectProperties,
  SeparateProvinceSelectorProperties,
  UniSelectProperties,
} from '@/pages/configuration/properties/select';
import {
  DatePickerProperties,
  DateSelectProperties,
  RangePickerProperties,
} from '@/pages/configuration/properties/date';
import {
  InputNumberProperties,
  InputProperties,
  PatAgeWithYMDProperties,
  RestrictInputNumberProperties,
} from '@/pages/configuration/properties/input';
import {
  DepartmentTransferTableProperties,
  IcdeDragTableProperties,
  IcuDragTableProperties,
  OperationDragTableProperties,
  PathologyIcdeDragTableProperties,
  TcmIcdeDragTableProperties,
} from '@/pages/configuration/properties/table';
import {
  BabyAgeProperties,
  FeeItemProperties,
  InHospitalOthersProperties,
  LabelProperties,
  OutTypeProperties,
  PostCodeProperties,
  SectionBottomProperties,
  SectionHeaderProperties,
  TimeRangeStatsProperties,
} from '@/pages/configuration/properties/others';
import ProvinceFormKeysInput from '@/pages/configuration/components/form/province-input';
import OutTypeLabelMappingInput from '@/pages/configuration/components/form/out-type-input';
import TimeRangeStatsItemsInput from '@/pages/configuration/components/form/time-range-input';
import FeeItemsInput from '@/pages/configuration/components/form/fee-item-input';
import { Button } from 'antd';
import TableColumnsConfiguration from '@/pages/configuration/components/form/table-columns';
import IcdeSelectFormKeysInput from '@/pages/configuration/components/form/form-keys-input';
import FormKeyInput from '@/pages/configuration/components/form/form-key-input';
import {
  icdeFormKeysToKey,
  icdeSelectTypeToKeys,
} from '@/pages/configuration/mapping/icde';
import { tableToFormKeyAndTableIdMapping } from '@/pages/configuration/mapping/table';
import {
  addressItemsProcessor,
  commonFormKeyToKeysMapping,
} from '@/pages/configuration/mapping/common';
import ProvinceFormKeysRequire from '@/pages/configuration/components/form/province-require';
import ColorSelector from '@/pages/configuration/components/form/color-selector';
import { isEmptyValues } from '@uni/utils/src/utils';
import ValueDependencyItemsInput from '@/pages/configuration/components/form/value-dependency';
import HospInutType from '../form/hosp-input-type/index';

export const propertiesPostProcessorMapping = {
  'data.key': (value, form) => {
    let formKey = form.getFieldValue('data.props.formKey');
    let component = form.getFieldValue('data.component');
    // 排除unique key
    if (!icdeFormKeysToKey[formKey]) {
      form.setFieldValue('data.props.formKey', value);
    }
  },

  'data.props.formKey': (value, form) => {
    let processed = false;
    let component = form.getFieldValue('data.component');
    // 同步写一套 data.key
    if (icdeFormKeysToKey[value]) {
      let componentName = form.getFieldValue('data.component');
      if (value && componentName && icdeFormKeysToKey[value][componentName]) {
        processed = true;
        form.setFieldValue('data.key', icdeFormKeysToKey[value][componentName]);
      }
    }

    // 其他的一套 像是 31天再住院 药物过敏这种
    if (commonFormKeyToKeysMapping[value]) {
      processed = true;
      Object.keys(commonFormKeyToKeysMapping[value])?.forEach((key) => {
        form.setFieldValue(key, commonFormKeyToKeysMapping[value]?.[key]);
      });
    }

    if (processed === false) {
      form.setFieldValue('data.key', value);
    }
  },

  'data.component': (value, form) => {
    // 诊断手术
    let formKey = form.getFieldValue('data.props.formKey');
    if (icdeFormKeysToKey[formKey]) {
      if (value && formKey && icdeFormKeysToKey[formKey][value]) {
        form.setFieldValue('data.key', icdeFormKeysToKey[formKey][value]);
      }
    }

    // 表格
    if (value?.indexOf('Table') > -1) {
      if (tableToFormKeyAndTableIdMapping[value]) {
        Object.keys(tableToFormKeyAndTableIdMapping[value])?.forEach((key) => {
          form.setFieldValue(
            key,
            tableToFormKeyAndTableIdMapping[value]?.[key],
          );
        });
      }
    }

    // 离院方式
    if (value === 'OutType') {
      form.setFieldValue('data.key', 'OutHospital');
      form.setFieldValue('data.props.formKey', '');
    }

    // 限定一套initialValue
    if (value === 'SectionHeader') {
      if (
        isEmptyValues(form.getFieldValue('data.props.sectionHeaderFontColor'))
      ) {
        form.setFieldValue('data.props.sectionHeaderFontColor', '#1D1D1D');
      }

      if (
        isEmptyValues(
          form.getFieldValue('data.props.sectionHeaderBackgroundColor'),
        )
      ) {
        form.setFieldValue(
          'data.props.sectionHeaderBackgroundColor',
          '#E9F3F3',
        );
      }

      if (
        isEmptyValues(form.getFieldValue('data.props.sectionHeaderBorderColor'))
      ) {
        form.setFieldValue('data.props.sectionHeaderBorderColor', '#70B7B7');
        form.setFieldValue('data.props.sectionBottomBorderColor', '#70B7B7');
      }
    }

    if (value === 'SectionBottom') {
      if (
        isEmptyValues(form.getFieldValue('data.props.sectionBottomBorderColor'))
      ) {
        form.setFieldValue('data.props.sectionBottomBorderColor', '#70B7B7');
      }
    }

    // 地址
    if (value === 'Input' || value === 'ProvinceSeparateSelector') {
      addressItemsProcessor(value, form);
    }
  },

  // 直接限定
  'data.props.icdeSelectType': (value, form) => {
    if (value && icdeSelectTypeToKeys[value]) {
      Object.keys(icdeSelectTypeToKeys[value])?.forEach((key) => {
        form.setFieldValue(key, icdeSelectTypeToKeys[value]?.[key]);
      });
    }
  },

  'data.props.hideCity': (value, form) => {
    form.setFieldValue('data.props.hideDistrict', value);
    form.setFieldValue('data.props.itemFormKeys.1', '');
    form.setFieldValue('data.props.itemFormKeys.2', '');
  },

  'data.props.hideDistrict': (value, form) => {
    form.setFieldValue('data.props.hideDistrict', value);
    form.setFieldValue('data.props.itemFormKeys.2', '');
  },

  'data.props.showDetailAddress': (value, form) => {
    if (value?.toString() === 'false') {
      form.setFieldValue('data.props.itemFormKeys.3', '');
    }
  },

  'data.props.hasValueDependency': (value, form) => {
    if (value?.toString() === 'true') {
      form.setFieldValue('data.props.valueDependencyClear', true);
      form.setFieldValue('data.props.valueDependencyTrueRequired', true);
    }

    if (value?.toString() === 'false') {
      form.setFieldValue('data.props.valueDependencies', []);
    }
  },
};

export const propertyComponentMapping = {
  Input: ProFormText,
  Select: ProFormSelect,
  Digit: ProFormDigit,
  Switch: ProFormSwitch,
  ProvinceFormKeysInput: ProvinceFormKeysInput,
  ProvinceFormKeysRequire: ProvinceFormKeysRequire,
  OutTypeLabelMappingInput: OutTypeLabelMappingInput,
  TimeRangeStatsItemsInput: TimeRangeStatsItemsInput,
  FeeItemsInput: FeeItemsInput,
  DragTable: TableColumnsConfiguration,
  IcdeSelectFormKeysInput: IcdeSelectFormKeysInput,
  FormKeyInput: FormKeyInput,
  HospInutType: HospInutType,
  ColorSelector: ColorSelector,

  ValueDependencyItemsInput: ValueDependencyItemsInput,
};

export const DmrComponentMapping: ComponentItem[] = [
  // {
  //     label: "下拉框",
  //     component: "Select",
  //     properties: UniSelectProperties,
  // },
  {
    label: 'Section头部',
    component: 'SectionHeader',
    properties: SectionHeaderProperties,
  },

  {
    label: 'Section底部',
    component: 'SectionBottom',
    properties: SectionBottomProperties,
  },
  {
    label: '病案首页定制下拉框',
    component: 'DmrSelect',
    properties: DmrSelectProperties,
  },
  // {
  //     label: "时间选择",
  //     component: "DatePicker",
  //     properties: DatePickerProperties
  // },
  // {
  //     label: "时间段选择",
  //     component: "RangePicker",
  //     properties: RangePickerProperties
  // },
  // {
  //     label: "省市区选择",
  //     component: "ProvinceSelector"
  // },
  {
    label: '省市区选择',
    component: 'ProvinceSeparateSelector',
    properties: SeparateProvinceSelectorProperties,
  },
  {
    label: '输入框',
    component: 'Input',
    properties: InputProperties,
  },
  // {
  //     label: "数字输入框",
  //     component: "InputNumber",
  //     properties: InputNumberProperties
  // },
  // {
  //     label: "表格",
  //     component: "Table"
  // },

  // 空组件
  {
    label: '空占位组件',
    component: 'Null',
  },

  // 入院途径 组件
  {
    label: '入院途径',
    component: 'AdmissionPathType',
    properties: InHospitalOthersProperties,
  },

  // 离院方式 组件
  {
    label: '离院方式',
    component: 'OutType',
    properties: OutTypeProperties,
  },

  // 年月日选择 仅用于 病案首页
  {
    label: '年月日输入',
    component: 'DateSelect',
    properties: DateSelectProperties,
  },

  // input with suffix 仅用于病案首页
  // {
  //     label: "包含选项显示的输入框",
  //     component: "InputSuffix",
  //     properties: [] // TODO input suffix properties
  // },
  // 诊断 select
  {
    label: '诊断编码下拉选择',
    component: 'IcdeSelect',
    properties: IcdeSelectProperties,
  },
  // 人员
  // {
  //     label: "",
  //     component: "EmployeeAutoComplete"
  // },
  // 拖动table
  // {
  //   label: "拖动排序表格",
  //   component: "DragEditTable",
  //   properties: [], // TODO DragEditTable properties
  // },
  {
    label: '诊断表格',
    component: 'IcdeDragTable',
    properties: IcdeDragTableProperties,
  },
  {
    label: '手术表格',
    component: 'OperationDragTable',
    properties: OperationDragTableProperties,
  },
  {
    label: 'ICU表格',
    component: 'IcuDragTable',
    properties: IcuDragTableProperties,
  },
  {
    label: '病理诊断表格',
    component: 'PathologyIcdeDragTable',
    properties: PathologyIcdeDragTableProperties,
  },
  {
    label: '中医诊断表格',
    component: 'TcmIcdeDragTable',
    properties: TcmIcdeDragTableProperties,
  },
  // 天时分
  {
    label: '天时分',
    component: 'TimeRangeStats',
    properties: TimeRangeStatsProperties,
  },
  // 费用
  {
    label: '费用',
    component: 'FeeItem',
    properties: FeeItemProperties,
  },
  // 数字输入限制
  {
    label: '数字输入框(限制输入大小)',
    component: 'RestrictInputNumber',
    properties: RestrictInputNumberProperties,
  },
  // 转院
  {
    label: '转院科室组件',
    component: 'DepartmentTransfer',
    properties: DepartmentTransferTableProperties,
  },
  // 自动选择 code
  {
    label: '自动选择下拉框',
    component: 'AutoSelect',
    properties: AutoSelectProperties,
  },
  // 新生儿年龄
  {
    label: '新生儿年龄',
    component: 'BabyAge',
    properties: BabyAgeProperties,
  },
  // 邮编
  {
    label: '邮编',
    component: 'PostCode',
    properties: PostCodeProperties,
  },
  // 显示组件
  {
    label: '文案显示',
    component: 'Label',
    properties: LabelProperties,
  },

  {
    label: '统一年龄输入',
    component: 'PatAgeWithYMD',
    properties: PatAgeWithYMDProperties,
  },
];
