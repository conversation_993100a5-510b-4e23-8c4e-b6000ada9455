import { Card, Col, Form, Row } from 'antd';
import {
  ReviewHeaderErrorProgress,
  reviewHeaderErrorProgressItems,
  ReviewHeaderErrorTotals,
  ReviewHeaderGauge,
  ReviewHeaderProgress,
  ReviewHeaderTotals,
} from '@/pages/review/components/header';
import { useEffect, useRef, useState } from 'react';
import './index.less';
import { useRequest, useModel } from 'umi';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { BatchItem, TaskStatusSummary } from '@/pages/review/interface';
import { isEmptyValues } from '@uni/utils/src/utils';
import ScoreCommentDrawerContainer from '@/pages/review/components/score-comment';
import ReviewTable from '@/pages/review/components/review-table';
import { dmrAuditeeColumns } from '@/pages/review/columns';
import dayjs from 'dayjs';
import { examineSummarySpan, sortByBatchDate } from '@/pages/review/utils';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit';
import { useRouteProps } from '@uni/commons/src/route-context';
import ExportIconBtn from '@uni/components/src/backend-export';
import Search from './search';
import Stats from '../components/stats';

const examineType =
  (window as any).externalConfig?.['dmr']?.['examineType'] ?? 'Score';

const examineSummaryShow =
  (window as any).externalConfig?.['qualityExamine']?.['examineSummaryShow'] ??
  true;

const DmrReviewAuditee = () => {
  const taskCreateContainerRef = useRef(null);
  const [form] = Form.useForm();

  const { globalState } = useModel('@@qiankunStateFromMaster');

  const taskTableRef = useRef(null);

  const dmrPreviewContainerRef = useRef(null);

  const [selectedStatItem, setSelectedStatItem] = useState<string>('');
  const [searchParams, setSearchParams] = useState<any>({});
  const [batchArgs] = useState<any>({});

  const { examineMasterId } = useRouteProps();

  const [taskSummaryInfo, setTaskSummaryInfo] =
    useState<TaskStatusSummary>(undefined);

  useEffect(() => {
    latestBatchInfoReq();
    // if (isEmptyValues(batchId)) {
    //   if (isEmptyValues(globalState?.searchParams?.BatchItem)) {
    //     latestBatchInfoReq();
    //   } else {
    //     batchInfoProcessor(globalState?.searchParams?.BatchItem);
    //   }
    // }
  }, [examineMasterId]);

  useEffect(() => {
    latestBatchInfoReq();
  }, []);

  useEffect(() => {
    if (searchParams && (searchParams.Sdate || searchParams.Edate)) {
      taskStatusSummaryReq();
      taskTableRef?.current?.freshQueryTable();
    }
  }, [searchParams]);

  const getUserEmployeeCode = () => {
    // return '0007';
    // TODO 不写死..

    let userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');
    return userInfo?.EmployeeCode;
  };

  const { loading: latestBatchInfoLoading, run: latestBatchInfoReq } =
    useRequest(
      () => {
        let data = {};
        if (!isEmptyValues(examineMasterId)) {
          data['MasterId'] = examineMasterId;
        }

        return uniCommonService('Api/Dmr/DmrCardQualityExamine/GetBatches', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (response: RespVO<BatchItem[]>) => {
          let latestBatch = response?.data?.sort(sortByBatchDate)?.at(0);
          let defaultBatchItem = globalState?.searchParams?.BatchItem;
          const findBatchItem = response?.data?.find(
            (item) =>
              item.BatchId === defaultBatchItem?.BatchId &&
              item.MasterId === defaultBatchItem?.MasterId,
          );
          if (findBatchItem) {
            batchInfoProcessor(defaultBatchItem);
          } else {
            batchInfoProcessor(latestBatch);
          }
        },
      },
    );

  const batchInfoProcessor = (batchData: BatchItem) => {
    let coderParams = {
      Coder: getUserEmployeeCode(),
    };
    if ((userInfo?.Roles ?? [])?.includes('Admin')) {
      coderParams['Coder'] = undefined;
    }

    if (!isEmptyValues(batchData?.BatchArgs)) {
      try {
        const infoBatchArgs = JSON.parse(batchData?.BatchArgs);

        Object.keys(infoBatchArgs).forEach((key) => {
          if (key?.includes('date')) {
            batchArgs[key.charAt(0).toUpperCase() + key.slice(1)] =
              infoBatchArgs[key];
          } else {
            batchArgs[key] = infoBatchArgs[key];
          }
        });
      } catch (e) {
        console.log('BatchArgs Error', e);
      }
    }
    setSearchParams({
      ...coderParams,
      ...searchParams,
      MasterId: batchData?.MasterId,
      BatchId: batchData?.BatchId,
      Sdate: batchArgs?.['Sdate']
        ? dayjs(batchArgs?.['Sdate'])?.format('YYYY-MM-DD')
        : undefined,
      Edate: batchArgs?.['Edate']
        ? dayjs(batchArgs?.['Edate'])?.format('YYYY-MM-DD')
        : undefined,
      // 传参非必要
      batchInfo: batchData,
      batchArgs,
    });
    setTimeout(() => {
      // 首次请求
      if (!isEmptyValues(examineMasterId ?? searchParams?.MasterId)) {
        taskStatusSummaryReq();
        taskTableRef?.current?.freshQueryTable();
      }
    }, 100);
  };
  const { loading: taskStatusSummaryLoading, run: taskStatusSummaryReq } =
    useRequest(
      () => {
        return uniCommonService(
          'Api/Dmr/DmrCardQualityExamine/GetTaskStatusSummary',
          {
            method: 'POST',
            data: {
              MasterId: examineMasterId ?? searchParams.MasterId,
              ...searchParams,
            },
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<TaskStatusSummary>) => {
          let summaryInfo = response?.data;
          summaryInfo['CommentErrorTotal'] =
            reviewHeaderErrorProgressItems?.reduce(
              (accumulator, item) =>
                accumulator + (summaryInfo?.[item?.valueKey] ?? 0),
              0,
            );
          setTaskSummaryInfo(summaryInfo ?? {});
        },
      },
    );

  const userInfo = JSON.parse(sessionStorage?.getItem('userInfo') ?? '{}');

  return (
    <div id={'auditee-container'} className={'auditee-container'}>
      <div className={'batch-range-container'}>
        <Search
          form={form}
          searchParams={searchParams}
          setSearchParams={setSearchParams}
        />
      </div>

      {examineSummaryShow === true && (
        <Row gutter={[8, 8]} className={'stats-container'}>
          <Col span={examineSummarySpan?.[examineType]?.at(0)}>
            <ReviewHeaderTotals
              summaryInfo={taskSummaryInfo}
              loading={taskStatusSummaryLoading || latestBatchInfoLoading}
            />
          </Col>
          <Col span={examineSummarySpan?.[examineType]?.at(1)}>
            <ReviewHeaderProgress
              summaryInfo={taskSummaryInfo}
              loading={taskStatusSummaryLoading || latestBatchInfoLoading}
            />
          </Col>
          <Col span={examineSummarySpan?.[examineType]?.at(2)}>
            <ReviewHeaderErrorTotals
              summaryInfo={taskSummaryInfo}
              loading={taskStatusSummaryLoading || latestBatchInfoLoading}
            />
          </Col>

          {examineType === 'Score' && (
            <Col span={examineSummarySpan?.[examineType]?.at(3)}>
              <ReviewHeaderGauge
                summaryInfo={taskSummaryInfo}
                loading={taskStatusSummaryLoading || latestBatchInfoLoading}
              />
            </Col>
          )}

          {examineType === 'Comment' && (
            <Col span={examineSummarySpan?.[examineType]?.at(3)}>
              <ReviewHeaderErrorProgress
                summaryInfo={taskSummaryInfo}
                loading={taskStatusSummaryLoading || latestBatchInfoLoading}
              />
            </Col>
          )}
        </Row>
      )}
      <div className="review-person-summary-container">
        <Stats
          selectedStatItem={selectedStatItem}
          selectOption="RejectedTaskCnt"
          setSelectedStatItem={setSelectedStatItem}
          summaryInfo={taskSummaryInfo}
          loading={taskStatusSummaryLoading}
        />
      </div>
      <Card
        style={{ marginTop: 8 }}
        title={'评审结果'}
        extra={
          <>
            <ExportIconBtn
              getExternalExportConfig={() => {
                return {
                  isBackend: true,
                  backendObj: {
                    url: 'Api/Dmr/DmrCardQualityExamine/ExportGetTasks',
                    method: 'POST',
                    data: {
                      ...taskTableRef?.current?.getFastSelectKeyToStatuses(),
                      ...searchParams,
                      ...(selectedStatItem
                        ? { [`${selectedStatItem}Flag`]: true }
                        : {}),
                    },
                    fileName: '评审结果',
                  },
                };
              }}
            />
            <TableColumnEditButton
              columnInterfaceUrl={'Api/Dmr/DmrCardQualityExamine/GetTasks'}
              onTableRowSaveSuccess={(columns) => {
                taskTableRef?.current?.setTaskTableColumns(columns);
              }}
            />
          </>
        }
      >
        <ReviewTable
          id={'dmr-review-card-progress-table'}
          noBatchIdInData={true}
          selectedStatItem={selectedStatItem}
          searchParams={searchParams}
          taskTableRef={taskTableRef}
          dmrPreviewContainerRef={dmrPreviewContainerRef}
          scroll={{
            x: 'max-content',
            y: examineSummaryShow === false ? 570 : 345,
          }}
          onSummarySelectKeysFilter={(item) => {
            return ['All', 'Reviewed', 'Rejected', 'ReSubmitted']?.includes(
              item?.key,
            );
          }}
          summaryInfo={taskSummaryInfo}
          fastSelectDefaultKey={'Rejected'}
          taskExtraParams={() => {
            return {
              ...searchParams,
            };
          }}
          extraHiddenColumns={dmrAuditeeColumns}
          reviewProps={{
            showOtherExamineTask: true,
          }}
        />
      </Card>

      <ScoreCommentDrawerContainer
        tableReadonly={true}
        dmrReadonly={false}
        drawerContainerRef={dmrPreviewContainerRef}
        onScoreCommentReviewEnd={(taskId: number) => {
          taskStatusSummaryReq();
          // taskTableRef?.current?.freshQueryTable();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        onScoreCommentClose={(taskId: number) => {
          taskStatusSummaryReq();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        onScoreCommentTableRefresh={(taskId: number) => {
          // taskTableRef?.current?.queryTasksCurrent();
          taskStatusSummaryReq();
          taskTableRef?.current?.updateCurrentTaskId(taskId);
        }}
        getContainer={() => {
          return document.getElementById('auditee-container');
        }}
      />
    </div>
  );
};

export default DmrReviewAuditee;
