// TODO 单独调试
import { uniCommonService } from '@uni/services/src';
import { isEmptyValues } from '@uni/utils/src/utils';
import LoadingComponent from '@uni/components/src/loading/loading';
import { dynamic } from 'umi';

const dynamicComponent =
  (window as any).externalConfig?.['dynamic']?.['componentAlias'] ?? {};
// const dynamicComponent = {
//   DmrExamineReviewer: '@/pages/review/reviewer/index',
//   DmrExamineReviewee: '@/pages/review/auditee/index',
//   DmrExamineManagement: '@/pages/review/management/index',
//   DmrExamineAnalysis: '',
// }

const pathResolver = (path: string) => {
  let paths = path?.split('/');
  return {
    examineMasterId: paths?.at(-1),
  };
};

export const qiankun = {
  // 应用加载之前
  async bootstrap(props) {
    console.log('app2 bootstrap', props);
  },
  // 应用 render 之前触发
  async mount(props) {
    console.log('app2 mount', props);
  },
  // 应用卸载之后触发
  async unmount(props) {
    console.log('app2 unmount', props);
  },
};

let extraRoutes = [];

export function patchRoutes({ routes }) {
  // 直接重置 route
  // 确认需要动态路由再重置
  if (
    !isEmptyValues(extraRoutes) &&
    Object.keys(dynamicComponent)?.findIndex(
      (key) => key === 'DmrExamineReviewer',
    ) > -1
  ) {
    // 保留path中包含highlight或report的路由 ** 动态路由必须有的
    const routesToKeep = routes.filter((route) => {
      const path = route.path || '';
      return path.includes('highlight') || path.includes('report');
    });

    routes.splice(0, routes.length);
    extraRoutes
      ?.filter((item) => {
        return !isEmptyValues(dynamicComponent?.[item?.ComponentHint]);
      })
      ?.forEach((item) => {
        routes.push({
          path: `/${item?.MenuItemUrl}`,
          exact: true,
          wrappers: [require('@uni/commons/src/route-context').default],
          component: dynamic({
            loader: () => import(`${dynamicComponent?.[item?.ComponentHint]}`),
            loading: LoadingComponent,
          }),
          params: pathResolver(item?.MenuItemUrl),
        });
      });

    // 最后添加需要保留的路由
    routesToKeep.forEach((route) => {
      routes.push(route);
    });

    console.log('routes', routes, extraRoutes);
  }
}

export function render(oldRender) {
  uniCommonService('Api/Sys/ClientKitSys/GetDynamicMenuItems', {
    method: 'POST',
  }).then((response) => {
    if (
      !isEmptyValues(
        response?.data?.filter(
          (item) => item?.NavUrl === '/dmr/examine' && item?.IsHidden !== true,
        ),
      )
    ) {
      extraRoutes = response?.data?.filter(
        (item) => item?.NavUrl === '/dmr/examine' && item?.IsHidden !== true,
      );
    }
    oldRender();
  });
}
