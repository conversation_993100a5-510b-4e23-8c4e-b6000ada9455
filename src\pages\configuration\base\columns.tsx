import { Emitter } from '@uni/utils/src/emitter';
import React from 'react';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { Space, Popconfirm, Switch, Tag, Tooltip } from 'antd';
import { cliDeptTypes } from '@/pages/configuration/base/cliDepts/constants';
import { UniSelect } from '@uni/components/src';
import {
  ConfigurationMissingSelect,
  ConfigurationOperationDegreeSelect,
  ConfigurationOperationTypeSelect,
  ConfigurationReadonlyItem,
  ConfigurationSwitch,
} from '@/pages/configuration/base/components';
import ConfigurationDictSelect from '@/pages/configuration/base/components/dict-select';
import ConfigurationIcdeSelect from '@/pages/configuration/base/components/icde-select';
import ConfigurationOperationSelect from '@/pages/configuration/base/components/oper-select';
import ConfigurationCliDeptsSelect from '@/pages/configuration/base/components/cliDepts-select';
import { ProFormDatePicker } from '@uni/components/src/pro-form';
import IconBtn from '@uni/components/src/iconBtn';
import { useModel } from '@@/plugin-model/useModel';
import { CheckOutlined } from '@ant-design/icons';
import { filterDepartments } from './cliDepts/processor';

const icdeBaseColumns = (eventSuffix: string) => [
  {
    fixed: 'left',
    dataIndex: 'Code',
    title: '诊断编码',
    width: 200,
    editable: true,
    // align: 'center',
  },
  {
    fixed: 'left',
    dataIndex: 'Name',
    title: '诊断名称',
    editable: true,
    // align: 'center',
    width: 300,
  },
  {
    dataIndex: 'HqmsCode',
    width: 200,
    // align: 'center',
    filterType: 'search',
    renderEditCell: ({ row, onRowChange, onClose }) => {},
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationIcdeSelect
          type={'Hqms'}
          paramKeys={entity?.Types || []}
          parentId={'icde-configuration-container'}
          formKeys={{
            HqmsName: 'Name',
            HqmsCode: 'Code',
          }}
          onIcdeSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.DMR_HQMS_ICDE_SELECT}#${eventSuffix}`,
                `${ConfigurationEvents.DMR_HQMS_ICDE_SELECT_NAME}#${eventSuffix}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'HqmsName',
    width: 300,
    // align: 'center',
    readonly: true,
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.DMR_HQMS_ICDE_SELECT_NAME}#${eventSuffix}#${entity?.id}`}
        nameKey={'HqmsName'}
      />
    ),
  },
  {
    dataIndex: 'InsurCode',
    width: 200,
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationIcdeSelect
          type={'Insur'}
          paramKeys={entity?.Types || []}
          parentId={'icde-configuration-container'}
          formKeys={{
            InsurName: 'Name',
            InsurCode: 'Code',
          }}
          onIcdeSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.DMR_INSURANCE_ICDE_SELECT}#${eventSuffix}`,
                `${ConfigurationEvents.DMR_INSURANCE_ICDE_SELECT_NAME}#${eventSuffix}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'InsurName',
    width: 300,
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.DMR_INSURANCE_ICDE_SELECT_NAME}#${eventSuffix}#${entity?.id}`}
        nameKey={'InsurName'}
      />
    ),
  },
  {
    dataIndex: 'WtCode',
    width: 200,
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationIcdeSelect
          type={'Wt'}
          paramKeys={entity?.Types || []}
          parentId={'icde-configuration-container'}
          formKeys={{
            WtName: 'Name',
            WtCode: 'Code',
          }}
          onIcdeSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.DMR_WT_ICDE_SELECT}#${eventSuffix}`,
                `${ConfigurationEvents.DMR_WT_ICDE_SELECT_NAME}#${eventSuffix}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'WtName',
    width: 300,
    // align: 'center',
    readonly: true,
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.DMR_WT_ICDE_SELECT_NAME}#${eventSuffix}#${entity?.id}`}
        nameKey={'WtName'}
      />
    ),
  },
  {
    dataIndex: 'IsValid',
    title: '是否有效',
    align: 'center',
    width: 100,
    renderFormItem: () => <ConfigurationSwitch />,
    render: (node, record, index, action) => {
      return <span>{record['IsValid'] ? '是' : '否'}</span>;
    },
  },
];

// editableTable poeration
const operations = [
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 100,
    render: (node, record, index, action) => (
      <>
        <Space size={'middle'}>
          <IconBtn
            key="edit"
            type="edit"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          />
          <IconBtn
            key="delete"
            type="delete"
            openPop={true}
            popOnConfirm={() => {
              Emitter.emit(ConfigurationEvents.DMR_ICDE_DELETE, {
                index,
                record,
              });
            }}
          />
        </Space>
      </>
    ),
  },
];
// table operation
const editModalOperations = [
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 100,
    render: (node, record, index, action) => (
      <>
        <Space size={'middle'}>
          <IconBtn
            key="edit"
            type="edit"
            onClick={() => {
              Emitter.emit(ConfigurationEvents.DMR_ICDE_EDIT, {
                index,
                record,
              });
            }}
          />
          <IconBtn
            key="delete"
            type="delete"
            openPop={true}
            popOnConfirm={() => {
              Emitter.emit(ConfigurationEvents.DMR_ICDE_DELETE, {
                index,
                record,
              });
            }}
          />
        </Space>
      </>
    ),
  },
];

export const dictionaryTableColumns = (
  eventSuffix,
  globalState,
  selectedModuleKey,
) => [
  {
    order: 1,
    title: '默认值',
    fixed: 'left',
    orderable: true,
    dataIndex: 'IsDefault',
    align: 'center',
    visible: true,
    width: 75,
    render: (node, record, index) => {
      return <span>{record['IsDefault'] ? <CheckOutlined /> : ''}</span>;
    },
    renderFormItem: () => <ConfigurationSwitch />,
  },
  {
    order: 2,
    dataIndex: 'Code',
    title: '编码',
    visible: true,
    width: 150,
    fixed: 'left',
    // align: 'center',
  },
  {
    order: 3,
    dataIndex: 'Name',
    title: '名称',
    visible: true,
    width: 200,
    fixed: 'left',
    // align: 'center',
  },
  {
    order: 4,
    dataIndex: 'HqmsCode',
    width: 150,
    visible: true,
    // align: 'center',
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationDictSelect
          dataSource={globalState?.dictData?.Hqms?.[selectedModuleKey]}
          paramKeys={entity?.Types || []}
          parentId={'dict-dictionary-table'}
          formKeys={{
            HqmsName: 'Name',
            HqmsCode: 'Code',
          }}
          onDictSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.DMR_DICT_HQMS_NAME}#${eventSuffix}`,
                `${ConfigurationEvents.DMR_DICT_HQMS_NAME}#${eventSuffix}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    order: 5,
    dataIndex: 'HqmsName',
    width: 200,
    visible: true,
    // align: 'center',
    readonly: true,
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.DMR_DICT_HQMS_NAME}#${eventSuffix}#${entity?.id}`}
        nameKey={'HqmsName'}
      />
    ),
  },
  {
    dataIndex: 'InsurCode',
    width: 150,
    visible: true,
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationDictSelect
          // selectModuleEventName={`${ConfigurationEvents.DMR_DICT_INSUR_NAME}`}
          dataSource={globalState?.dictData?.Insur?.[selectedModuleKey]}
          paramKeys={entity?.Types || []}
          parentId={'dict-dictionary-table'}
          formKeys={{
            InsurName: 'Name',
            InsurCode: 'Code',
          }}
          onDictSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.DMR_DICT_INSUR_NAME}#${eventSuffix}`,
                `${ConfigurationEvents.DMR_DICT_INSUR_NAME}#${eventSuffix}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'InsurName',
    width: 200,
    visible: true,
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.DMR_DICT_INSUR_NAME}#${eventSuffix}#${entity?.id}`}
        nameKey={'InsurName'}
      />
    ),
  },
  {
    order: 6,
    dataIndex: 'WtCode',
    width: 150,
    visible: true,
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationDictSelect
          dataSource={globalState?.dictData?.Wt?.[selectedModuleKey]}
          paramKeys={entity?.Types || []}
          parentId={'dict-dictionary-table'}
          formKeys={{
            WtName: 'Name',
            WtCode: 'Code',
          }}
          onDictSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.DMR_DICT_WT_NAME}#${eventSuffix}`,
                `${ConfigurationEvents.DMR_DICT_WT_NAME}#${eventSuffix}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    order: 7,
    dataIndex: 'WtName',
    width: 200,
    visible: true,
    // align: 'center',
    readonly: true,
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.DMR_DICT_WT_NAME}#${eventSuffix}#${entity?.id}`}
        nameKey={'WtName'}
      />
    ),
  },
  {
    title: '有效',
    dataIndex: 'IsValid',
    align: 'center',
    visible: true,
    width: 80,
    render: (node, record, index) => {
      return <span>{record['IsValid'] ? '是' : '否'}</span>;
    },
    renderFormItem: () => <ConfigurationSwitch />,
  },
  {
    title: '操作',
    visible: true,
    align: 'center',
    valueType: 'option',
    fixed: 'right',
    width: 100,
    render: (node, record, index, action) => {
      return (
        <Space size={'middle'}>
          <IconBtn
            key="edit"
            type="edit"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          />
          <IconBtn
            key="delete"
            type="delete"
            openPop={true}
            popOnConfirm={() => {
              Emitter.emit(ConfigurationEvents.DMR_DICT_DELETE, {
                index,
                record,
              });
            }}
          />
        </Space>
        // <div className={'dictionary-item-operation'}>
        //   <a
        //     className={'operation-item'}
        //     onClick={() => {
        //       Emitter.emit(
        //         ConfigurationEvents.DICTIONARY_CONFIGURATION_ITEM_RELATION_ADD,
        //         index,
        //       );
        //     }}
        //   >
        //     修改对照
        //   </a>

        //   <a
        //     className={'operation-item'}
        //     onClick={() => {
        //       Emitter.emit(
        //         ConfigurationEvents.DICTIONARY_CONFIGURATION_ITEM_EDIT,
        //         index,
        //       );
        //     }}
        //   >
        //     修改
        //   </a>

        //   <Popconfirm
        //     title="确定删除？"
        //     getPopupContainer={(triggerNode) =>
        //       document.getElementById('dictionary-configuration-container')
        //     }
        //     onConfirm={() => {
        //       Emitter.emit(
        //         ConfigurationEvents.DICTIONARY_CONFIGURATION_ITEM_DELETE,
        //         index,
        //       );
        //     }}
        //   >
        //     <a className={'operation-item'}>删除</a>
        //   </Popconfirm>
        // </div>
      );
    },
    onCell: (record, index) => {
      if (record['span']) {
        return {
          rowSpan: record['span'],
        };
      }

      return {
        rowSpan: 0,
      };
    },
  },
];

export const icdeDictColumns = (eventSuffix: string) => [
  {
    dataIndex: 'IsObsolete',
    title: ' ',
    fixed: 'left',
    visible: true,
    align: 'center',
    readonly: true,
    width: 50,
    order: 1,
    render: (node, record, index, action) => {
      return (
        <>
          {record['IsObsolete'] ? (
            <Tooltip title={'置灰'}>
              <Tag
                style={{
                  margin: '5px',
                  padding: '0 5px',
                  border: 'none',
                  borderRadius: 4,
                }}
                color={'#eb5757'}
              >
                灰
              </Tag>
            </Tooltip>
          ) : (
            ''
          )}
        </>
      );
    },
  },
  ...icdeBaseColumns(eventSuffix),
  {
    dataIndex: 'IsMain',
    title: '主诊可用',
    align: 'center',
    width: 100,
    renderFormItem: () => <ConfigurationSwitch />,
    render: (node, record, index, action) => {
      return <span>{record['IsMain'] ? '是' : '否'}</span>;
    },
  },
  {
    title: '入院可用',
    dataIndex: 'IsAdm',
    align: 'center',
    width: 100,
    renderFormItem: () => <ConfigurationSwitch />,
    render: (node, record, index, action) => {
      return <span>{record['IsAdm'] ? '是' : '否'}</span>;
    },
  },
  {
    title: '出院可用',
    dataIndex: 'IsDscg',
    align: 'center',
    width: 100,
    renderFormItem: () => <ConfigurationSwitch />,
    render: (node, record, index, action) => {
      return <span>{record['IsAdm'] ? '是' : '否'}</span>;
    },
  },
  {
    dataIndex: 'IsDamgIntxExtRea',
    align: 'center',
    width: 100,
    renderFormItem: () => <ConfigurationSwitch />,
    render: (node, record, index, action) => {
      return <span>{record['IsDamgIntxExtRea'] ? '是' : '否'}</span>;
    },
  },
  {
    title: '门急诊可用',
    dataIndex: 'IsOtp',
    align: 'center',
    width: 100,
    renderFormItem: () => <ConfigurationSwitch />,
    render: (node, record, index, action) => {
      return <span>{record['IsOtp'] ? '是' : '否'}</span>;
    },
  },
  // {
  //   dataIndex: 'Types',
  //   title: '类型',
  //   visible: true,
  //   width: 300,
  //   filterType: 'filter',
  //   filters: icdeTypes?.map((d) => ({
  //     text: d.label,
  //     value: d.value,
  //   })),
  //   renderFormItem: () => <ConfigurationIcdeTypeSelect />,
  //   render: (node, record, index) => {
  //     return (
  //       <>
  //         {record['Types']?.length > 0 ? (
  //           record['Types'].map((key) => {
  //             let icdeItem = icdeTypes?.find((item) => item.value === key);
  //             return <Tag style={{ margin: '5px 5px' }}>{icdeItem?.label}</Tag>;
  //           })
  //         ) : (
  //           <span>未指定</span>
  //         )}
  //       </>
  //     );
  //   },
  // },
  ...editModalOperations,
];

export interface OperIcdeExtraMapItem {
  display: string;
  label: string;
  color: string;
  className?: string;
  style?: React.CSSProperties;
}

export const operationExtraMap: { [key: string]: OperIcdeExtraMapItem } = {
  IsMicro: {
    display: '微',
    label: '微创手术',
    color: 'blue',
  },
  IsObsolete: {
    display: '灰',
    label: '置灰',
    color: '#eb5757',
  },
  IsDaySurgery: {
    display: '日',
    label: '日间手术',
    color: 'volcano',
  },
  IsTcm: {
    display: '中',
    label: '中医',
    color: 'purple',
  },

  HqmsDegree: {
    display: '',
    label: '',
    color: '',
  },

  DrgsDegree: {
    display: '',
    label: '',
    color: '',
  },
};

export const hqmsDegreeMap = {
  '3': {
    color: 'red',
    display: '国三',
    label: '国三手术',
  },
  '4': {
    color: 'red',
    display: '国四',
    label: '国四手术',
  },
};

export const drgsDegreeMap = {
  '1': {
    color: 'gold',
    display: '一',
    label: '一级手术',
  },
  '2': {
    color: 'gold',
    display: '二',
    label: '二级手术',
  },
  '3': {
    color: 'gold',
    display: '三',
    label: '三级手术',
  },
  '4': {
    color: 'gold',
    display: '四',
    label: '四级手术',
  },
};
interface OperationExtraTagsItemProps {
  record: any;
  value?: string[];
}

export const OperationExtraTagsItem = (props: OperationExtraTagsItemProps) => {
  let valueKeys = [
    'HqmsDegree',
    'DrgsDegree',
    'IsMicro',
    'IsObsolete',
    'IsTcm',
    'IsDaySurgery',
  ];

  // 国考手术等级
  if (
    props?.record['HqmsDegree'] &&
    hqmsDegreeMap[props?.record['HqmsDegree']]
  ) {
    operationExtraMap['HqmsDegree']['display'] =
      hqmsDegreeMap[props?.record['HqmsDegree']]?.display;
    operationExtraMap['HqmsDegree']['label'] =
      hqmsDegreeMap[props?.record['HqmsDegree']]?.label;
    operationExtraMap['HqmsDegree']['color'] =
      hqmsDegreeMap[props?.record['HqmsDegree']]?.color;
  } else {
    // 把degree剔除
    valueKeys = valueKeys?.filter((key) => key !== 'HqmsDegree');
  }

  // DRGS手术等级
  if (
    props?.record['DrgsDegree'] &&
    drgsDegreeMap[props?.record['DrgsDegree']]
  ) {
    operationExtraMap['DrgsDegree']['display'] =
      drgsDegreeMap[props?.record['DrgsDegree']]?.display;
    operationExtraMap['DrgsDegree']['label'] =
      drgsDegreeMap[props?.record['DrgsDegree']]?.label;
    operationExtraMap['DrgsDegree']['color'] =
      drgsDegreeMap[props?.record['DrgsDegree']]?.color;
  } else {
    // 把degree剔除
    valueKeys = valueKeys?.filter((key) => key !== 'DrgsDegree');
  }

  if (!props?.record['IsMicro']) {
    valueKeys = valueKeys?.filter((key) => key !== 'IsMicro');
  }
  if (!props?.record['IsObsolete']) {
    valueKeys = valueKeys?.filter((key) => key !== 'IsObsolete');
  }
  if (!props?.record['IsTcm']) {
    valueKeys = valueKeys?.filter((key) => key !== 'IsTcm');
  }
  if (!props?.record['IsDaySurgery']) {
    valueKeys = valueKeys?.filter((key) => key !== 'IsDaySurgery');
  }

  return (
    <>
      {valueKeys?.length > 0 ? (
        valueKeys.map((key) => {
          return (
            <Tooltip title={operationExtraMap[key]?.label}>
              <Tag
                style={{ margin: '5px 5px', border: 'none', borderRadius: 4 }}
                color={operationExtraMap[key]?.color}
              >
                {operationExtraMap[key]?.display}
              </Tag>
            </Tooltip>
          );
        })
      ) : (
        <span>-</span>
      )}
    </>
  );
};

const operOperations = [
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 100,
    render: (node, record, index, action) => (
      <>
        <Space size={'middle'}>
          <IconBtn
            key="edit"
            type="edit"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          />
          <IconBtn
            key="delete"
            type="delete"
            openPop={true}
            popOnConfirm={() => {
              Emitter.emit(ConfigurationEvents.DMR_OPER_DELETE, {
                index,
                record,
              });
            }}
          />
        </Space>
      </>
    ),
  },
];

const operEditOperations = [
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 100,
    render: (node, record, index, action) => (
      <>
        <Space size={'middle'}>
          <IconBtn
            key="edit"
            type="edit"
            onClick={() => {
              Emitter.emit(ConfigurationEvents.DMR_OPER_EDIT, {
                index,
                record,
              });
            }}
          />
          <IconBtn
            key="delete"
            type="delete"
            openPop={true}
            popOnConfirm={() => {
              Emitter.emit(ConfigurationEvents.DMR_OPER_DELETE, {
                index,
                record,
              });
            }}
          />
        </Space>
      </>
    ),
  },
];

export const operDictColumns = (eventSuffix: string, dictData) => [
  {
    dataIndex: 'Operation',
    order: 1,
    fixed: 'left',
    title: ' ',
    visible: true,
    align: 'center',
    readonly: true,
    width: 45,
    render: (node, record, index, action) => {
      return (
        <>
          <OperationExtraTagsItem record={record} />
        </>
      );
    },
  },
  {
    dataIndex: 'OperType',
    title: '手术类型',
    fixed: 'left',
    align: 'center',
    width: 90,
    renderFormItem: () => <ConfigurationOperationTypeSelect />,
    filterType: 'filter',
    filters: dictData?.OperType?.map((d) => ({
      text: d.Name,
      value: d.Code,
    })),
  },
  {
    dataIndex: 'Code',
    title: '手术编码',
    fixed: 'left',
    width: 150,
    // align: 'center',
  },
  {
    dataIndex: 'Name',
    title: '手术名称',
    fixed: 'left',
    // align: 'center',
    width: 250,
  },
  {
    dataIndex: 'Degree',
    title: '手术级别',
    fixed: 'left',
    align: 'center',
    width: 80,
    renderFormItem: () => <ConfigurationOperationDegreeSelect />,
    filterType: 'filter',
    filters: dictData?.SSJB?.map((d) => ({
      text: d.Name,
      value: d.Code,
    })),
  },
  {
    dataIndex: 'InsurCode',
    width: 150,
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationOperationSelect
          type={'Insur'}
          formKeys={{
            InsurName: 'Name',
            InsurCode: 'Code',
          }}
          parentId={'operation-dictionary-table'}
          onOperationSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.DMR_INSURANCE_OPER_SELECT}#${eventSuffix}`,
                `${ConfigurationEvents.DMR_INSURANCE_OPER_SELECT_NAME}#${eventSuffix}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'InsurName',
    // align: 'center',
    width: 250,
    readonly: true,
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.DMR_INSURANCE_OPER_SELECT_NAME}#${eventSuffix}#${entity?.id}`}
        nameKey={'InsurName'}
      />
    ),
  },
  {
    dataIndex: 'HqmsCode',
    width: 150,
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationOperationSelect
          type={'Hqms'}
          formKeys={{
            HqmsName: 'Name',
            HqmsCode: 'Code',
          }}
          parentId={'operation-dictionary-table'}
          onOperationSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.DMR_HQMS_OPER_SELECT}#${eventSuffix}`,
                `${ConfigurationEvents.DMR_HQMS_OPER_SELECT_NAME}#${eventSuffix}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'HqmsName',
    // align: 'center',
    width: 250,
    readonly: true,
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.DMR_HQMS_OPER_SELECT_NAME}#${eventSuffix}#${entity?.id}`}
        nameKey={'HqmsName'}
      />
    ),
  },
  {
    dataIndex: 'WtCode',
    width: 150,
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationOperationSelect
          type={'Wt'}
          formKeys={{
            WtName: 'Name',
            WtCode: 'Code',
          }}
          parentId={'operation-dictionary-table'}
          onOperationSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.DMR_WT_OPER_SELECT}#${eventSuffix}`,
                `${ConfigurationEvents.DMR_WT_OPER_SELECT_NAME}#${eventSuffix}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'WtName',
    // align: 'center',
    width: 250,
    readonly: true,
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.DMR_WT_OPER_SELECT_NAME}#${eventSuffix}#${entity?.id}`}
        nameKey={'WtName'}
      />
    ),
  },
  {
    dataIndex: 'NamePy',
  },
  {
    dataIndex: 'NameWb',
  },
  {
    dataIndex: 'NameCustom',
  },
  {
    dataIndex: 'NamePyAll',
  },
  {
    dataIndex: 'IsMicro',
  },
  {
    dataIndex: 'IsDaySurgery',
  },
  {
    dataIndex: 'IsObsolete',
  },
  {
    dataIndex: 'IsValid',
    align: 'center',
    width: 60,
    renderFormItem: () => <ConfigurationSwitch />,
    render: (node, record, index, action) => {
      return <span>{record['IsValid'] ? '是' : '否'}</span>;
    },
  },
  ...operEditOperations,
];

export const pathologyIcdeDictColumns = (eventSuffix: string) => [
  ...icdeBaseColumns(eventSuffix),
  ...editModalOperations,
];

export const tcmIcdeDictColumns = (eventSuffix: string, dictData) => [
  // {
  //   order: 2,
  //   fixed: 'left',
  //   dataIndex: 'Code',
  //   title: '诊断唯一编码',
  //   visible: false,
  //   width: 200,
  //   editable: true,
  //   // align: 'center',
  // },
  {
    fixed: 'left',
    dataIndex: 'Code', // TcmIcdeCode
    title: '诊断编码',
    width: 200,
    editable: true,
    // align: 'center',
  },
  ...icdeBaseColumns(eventSuffix)?.slice(1),
  {
    dataIndex: 'IsTcm',
    // visible: false,
    align: 'center',
    width: 100,
    renderFormItem: () => <ConfigurationSwitch />,
    render: (node, record, index, action) => {
      return <span>{record['IsTcm'] ? '是' : '否'}</span>;
    },
  },
  // {
  //   dataIndex: 'IsZb',
  //   title: '主病',
  //   visible: true,
  //   width: 80,
  //   align: 'center',
  //   renderFormItem: () => <ConfigurationSwitch />,
  //   render: (node, record, index) => {
  //     return <span>{record['IsZb'] ? '是' : '否'}</span>;
  //   },
  // },
  // {
  //   dataIndex: 'IsZz',
  //   title: '主症',
  //   visible: true,
  //   width: 80,
  //   align: 'center',
  //   renderFormItem: () => <ConfigurationSwitch />,
  //   render: (node, record, index) => {
  //     return <span>{record['IsZz'] ? '是' : '否'}</span>;
  //   },
  // },
  {
    dataIndex: 'TcmIcdeCategory',
    align: 'center',
    width: 100,
    fixed: 'left',
    filterType: 'filter',
    filters: dictData?.TcmIcdeCategory?.map((d) => {
      return {
        text: d.Name,
        value: d.Code,
      };
    }),
  },
  ...editModalOperations,
];

// hospital
export const hospitalDictColumns = [
  {
    dataIndex: 'HospCode',
    title: '医院代码',
    visible: true,
    width: 110,
    align: 'center',
    editable: false,
    filterType: 'search',
  },
  {
    dataIndex: 'HospName',
    title: '医院名称',
    visible: true,
    align: 'center',
    filterType: 'search',
  },
  {
    dataIndex: 'HospNameAbbr',
    title: '医院简称',
    visible: true,
    align: 'center',
    width: 110,
    filterType: 'search',
  },
  {
    dataIndex: 'InsurOrganizationCode',
    visible: true,
    title: '医保定点机构代码',
    align: 'center',
    width: 170,
    filterType: 'search',
  },
  {
    dataIndex: 'InsurOrganizationName',
    visible: true,
    title: '医保定点机构名称',
    align: 'center',
    width: 170,
    filterType: 'search',
  },
  {
    dataIndex: 'OrganizationCode',
    title: '机构代码',
    visible: true,
    align: 'center',
    width: 110,
    filterType: 'search',
  },
  {
    dataIndex: 'OrganizationName',
    title: '机构名称',
    visible: true,
    align: 'center',
    width: 110,
    filterType: 'search',
  },
  {
    dataIndex: 'HospArea',
    title: '行政区号',
    visible: true,
    align: 'center',
    width: 100,
  },
  {
    dataIndex: 'HospAddr',
    title: '医院地址',
    visible: true,
    align: 'center',
    width: 100,
  },
  {
    dataIndex: 'HospConstructDate',
    title: '建院日期',
    visible: true,
    align: 'center',
    width: 120,
    renderFormItem: ({ index, entity }) => {
      return (
        <ProFormDatePicker
          className="date-picker-hosp-configuration"
          initialValue={entity?.HospConstructDate}
        />
      );
    },
  },
  {
    dataIndex: 'HospTel',
    title: '医院电话',
    visible: true,
    align: 'center',
    width: 110,
    // renderFormItem: ({ index, entity },props) => {
    //   return <ProFormDigit {...props?.fieldProps} initialValue={entity?.HospTel} />
    // }
  },
  {
    dataIndex: 'HospType',
    title: '医院等级',
    visible: true,
    align: 'center',
    width: 100,
  },
  {
    dataIndex: 'HospAcreage',
    title: '医用面积',
    visible: true,
    align: 'center',
    width: 100,
  },
  {
    dataIndex: 'DeanName',
    title: '院长姓名',
    visible: true,
    align: 'center',
    width: 100,
  },
  {
    dataIndex: 'StatsPrincipal',
    visible: true,
    align: 'center',
    width: 110,
  },
  {
    dataIndex: 'FinancePrincipal',
    visible: true,
    align: 'center',
    width: 110,
  },
  {
    dataIndex: 'StatsLister',
    visible: true,
    align: 'center',
    width: 110,
  },
  {
    dataIndex: 'ReportAuditor',
    visible: true,
    align: 'center',
    width: 110,
  },
  {
    dataIndex: 'HospOwnership',
    visible: true,
    align: 'center',
    width: 110,
  },
  {
    dataIndex: 'IsValid',
    title: '有效',
    visible: true,
    align: 'center',
    width: 80,
    render: (node, record, index) => {
      return <span>{record['IsValid'] ? '是' : '否'}</span>;
    },
    renderFormItem: ({ index, entity }, props) => {
      return <Switch {...props?.fieldProps} defaultChecked={entity?.IsValid} />;
    },
  },
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    fixed: 'right',
    order: Number.MAX_VALUE,
    width: 100,
    render: (node, record, index, action) => {
      return (
        <div className={'dictionary-item-operation'}>
          <IconBtn
            type="edit"
            onClick={() => {
              action?.startEditable?.(record.HospId);
            }}
          />
        </div>
      );
    },
  },
];

export const cliDeptsDictColumns = (dictData, majorPerfDeptData) => [
  {
    dataIndex: 'Code',
    title: '科室编码',
    visible: true,
    // width: 110,
    // align: 'center',
    filterType: 'search',
    order: 1,
    editable: false,
  },
  {
    dataIndex: 'Name',
    title: '科室名称',
    visible: true,
    // align: 'center',
    // width: 110,
    filterType: 'search',
    order: 2,
  },
  {
    dataIndex: 'CatyName',
    title: '科别',
    visible: true,
    align: 'center',
    renderFormItem: () => {
      return (
        <UniSelect
          dataSource={dictData?.Caty}
          labelInValue
          placeholder={'请选择'}
          optionNameKey={'Name'}
          optionValueKey={'Code'}
        />
      );
    },
  },
  {
    dataIndex: 'HospName',
    title: '所属院区',
    visible: true,
    align: 'center',
    // width: 100,
    filterType: 'filter',
    renderFormItem: ({ index, entity }) => {
      return (
        <UniSelect
          dataSource={dictData?.Hospital}
          placeholder={'请选择'}
          labelInValue
          optionNameKey={'Name'}
          optionValueKey={'Code'}
          allowClear={false}
        />
      );
    },
  },
  {
    dataIndex: 'ApprovedBedAmt',
    title: '核定床位数',
    visible: true,
    readonly: true,
    // width: 150,
    order: 5,
  },
  {
    dataIndex: 'OpenBedAmt',
    title: '开放床位数',
    readonly: true,
    visible: true,
    // width: 150,
    order: 6,
  },
  {
    dataIndex: 'IsValid',
    title: '有效',
    visible: true,
    align: 'center',
    // width: 80,
    render: (node, record, index) => {
      return <span>{record['IsValid'] ? '是' : '否'}</span>;
    },
    renderFormItem: ({ index, entity }, props) => {
      return <Switch {...props?.fieldProps} defaultChecked={entity?.IsValid} />;
    },
  },
  {
    dataIndex: 'SrcCode',
    title: '对照编码',
    // width: 100,
    align: 'center',
    filterType: 'search',
  },
  {
    dataIndex: 'SrcName',
    title: '对照名称',
    align: 'center',
    // width: 300,
    filterType: 'search',
  },
  {
    dataIndex: 'MajorPerfDeptName',
    renderFormItem: (_, props) => {
      return (
        <UniSelect
          dataSource={
            filterDepartments(majorPerfDeptData, props?.record?.CliDeptTypes) ||
            []
          }
          labelInValue
          placeholder={'请选择'}
          optionNameKey={'Name'}
          optionValueKey={'Code'}
        />
      );
    },
  },
  {
    dataIndex: 'CliDeptTypes',
    title: '科室类型',
    visible: true,
    width: 300,
    filterType: 'filter',
    filters: [...cliDeptTypes, { label: '未指定', value: 'null' }]?.map(
      (d) => ({
        text: d.label,
        value: d.value,
      }),
    ),
    renderFormItem: ({ index, entity }) => {
      return (
        <UniSelect
          dataSource={cliDeptTypes}
          placeholder={'请选择'}
          optionNameKey={'label'}
          maxTagCount={'responsive'}
          mode={'multiple'}
        />
      );
    },
    render: (node, record, index) => {
      return (
        <>
          {record['CliDeptTypes']?.length > 0 &&
          record['CliDeptTypes']?.at(0) !== 'null' ? (
            record['CliDeptTypes'].map((key) => {
              let cliDeptItem = cliDeptTypes?.find(
                (item) => item.value === key,
              );
              return (
                <Tag style={{ margin: '5px 5px' }}>{cliDeptItem?.label}</Tag>
              );
            })
          ) : (
            <span>未指定</span>
          )}
        </>
      );
    },
  },
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    order: Number.MAX_VALUE,
    align: 'center',
    fixed: 'right',
    width: 100,
    render: (node, record, index, action) => {
      return (
        <Space size={'middle'}>
          <IconBtn
            key="edit"
            type="edit"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          />
          <IconBtn
            key="delete"
            type="delete"
            openPop={true}
            popOnConfirm={() => {
              Emitter.emit(
                ConfigurationEvents.CLI_DEPTS_CONFIGURATION_ITEM_DELETE,
                {
                  index,
                  record,
                },
              );
            }}
          />
          <IconBtn
            title={'查看床位数列表'}
            key="details"
            type="details"
            onClick={() => {
              Emitter.emit(ConfigurationEvents.HIERARCHY_BED_EDIT, {
                index,
                record,
              });
            }}
          />
        </Space>
      );
    },
  },
];

export const wardsDictColumns = (dictData) => [
  {
    dataIndex: 'Code',
    title: '病区编码',
    visible: true,
    // width: 150,
    // align: 'center',
    filterType: 'search',
    order: 1,
    editable: false,
  },
  {
    dataIndex: 'Name',
    title: '病区名称',
    visible: true,
    // align: 'center',
    // width: 200,
    filterType: 'search',
    order: 2,
  },
  {
    dataIndex: 'ApprovedBedAmt',
    title: '核定床位数',
    visible: true,
    // width: 150,
    order: 5,
    readonly: true,
  },
  {
    dataIndex: 'OpenBedAmt',
    title: '开放床位数',
    visible: true,
    // width: 150,
    order: 6,
    readonly: true,
  },
  {
    dataIndex: 'CatyName',
    title: '科别',
    visible: true,
    align: 'center',
    renderFormItem: () => {
      return (
        <UniSelect
          dataSource={dictData?.Caty}
          labelInValue
          placeholder={'请选择'}
          optionNameKey={'Name'}
          optionValueKey={'Code'}
        />
      );
    },
  },
  {
    dataIndex: 'HospName',
    title: '所属院区',
    visible: true,
    align: 'center',
    // width: 100,
    filterType: 'filter',
    renderFormItem: ({ index, entity }) => {
      return (
        <UniSelect
          dataSource={dictData?.Hospital}
          placeholder={'请选择'}
          labelInValue
          optionNameKey={'Name'}
          optionValueKey={'Code'}
          allowClear={false}
        />
      );
    },
  },
  {
    dataIndex: 'MajorPerfDeptName',
    renderFormItem: ({ index, entity }) => {
      return (
        <UniSelect
          dataSource={dictData?.MajorPerfDepts}
          labelInValue
          placeholder={'请选择'}
          optionNameKey={'Name'}
          optionValueKey={'Code'}
        />
      );
    },
  },
  {
    dataIndex: 'IsValid',
    title: '是否有效',
    visible: true,
    align: 'center',
    // width: 100,
    render: (node, record, index) => {
      return <span>{record['IsValid'] ? '是' : '否'}</span>;
    },
    renderFormItem: () => <ConfigurationSwitch />,
    // renderFormItem: ({ index, entity }, props) => {
    //   return <Switch {...props?.fieldProps} defaultChecked={entity?.IsValid} />;
    // },
  },
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    order: Number.MAX_VALUE,
    align: 'center',
    fixed: 'right',
    width: 100,
    render: (node, record, index, action) => {
      return (
        <Space size={'middle'}>
          <IconBtn
            key="edit"
            type="edit"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          />
          <IconBtn
            key="delete"
            type="delete"
            openPop={true}
            popOnConfirm={() => {
              Emitter.emit(
                ConfigurationEvents.CLI_DEPTS_CONFIGURATION_ITEM_DELETE,
                {
                  index,
                  record,
                },
              );
            }}
          />
          <IconBtn
            title={'查看床位数列表'}
            key="details"
            type="details"
            onClick={() => {
              Emitter.emit(ConfigurationEvents.HIERARCHY_BED_EDIT, {
                index,
                record,
              });
            }}
          />
        </Space>
      );
    },
  },
];

export const cliDeptCategoryTableColumns = [
  {
    dataIndex: 'Code',
    title: '编码',
    visible: true,
    width: '15%',
    align: 'center',
    filterType: 'search',
    onCell: (record, index) => {
      if (record['span']) {
        return {
          rowSpan: record['span'],
        };
      }

      return {
        rowSpan: 0,
      };
    },
  },
  {
    dataIndex: 'Name',
    title: '名称',
    visible: true,
    align: 'center',
    filterType: 'search',
    onCell: (record, index) => {
      if (record['span']) {
        return {
          rowSpan: record['span'],
        };
      }

      return {
        rowSpan: 0,
      };
    },
  },
  {
    dataIndex: 'SrcCode',
    title: '对照编码',
    visible: true,
    width: '15%',
    align: 'center',
    filterType: 'search',
  },
  {
    dataIndex: 'SrcName',
    title: '对照名称',
    visible: true,
    align: 'center',
    filterType: 'search',
  },
  {
    title: '有效',
    dataIndex: 'IsValid',
    align: 'center',
    visible: true,
    width: 80,
    render: (node, record, index) => {
      return <span>{record['IsValid'] ? '是' : '否'}</span>;
    },
    renderFormItem: ({ index, entity }, props) => {
      return <Switch {...props?.fieldProps} defaultChecked={entity?.IsValid} />;
    },
    onCell: (record, index) => {
      if (record['span']) {
        return {
          rowSpan: record['span'],
        };
      }

      return {
        rowSpan: 0,
      };
    },
  },
  {
    title: '操作',
    visible: true,
    align: 'center',
    fixed: 'right',
    width: 150,
    render: (node, recode, index) => {
      return (
        <div className={'dictionary-item-operation'}>
          <a
            className={'operation-item'}
            onClick={() => {
              Emitter.emit(
                ConfigurationEvents.CLIDEPTS_CATEGORY_ITEM_RELATION_ADD,
                index,
              );
            }}
          >
            修改对照
          </a>

          <a
            className={'operation-item'}
            onClick={() => {
              Emitter.emit(
                ConfigurationEvents.CLIDEPTS_CATEGORY_ITEM_EDIT,
                index,
              );
            }}
          >
            修改
          </a>
        </div>
      );
    },
    onCell: (record, index) => {
      if (record['span']) {
        return {
          rowSpan: record['span'],
        };
      }

      return {
        rowSpan: 0,
      };
    },
  },
];

export const missingDictColumns = [
  {
    dataIndex: 'Directories',
    title: '字典',
    visible: true,
    align: 'center',
    width: 100,
    readonly: true,
    renderFormItem: ({ index, entity }) => (
      <span>{entity['Directories']?.join('/')}</span>
    ),
    render: (node, record, index, action) => {
      return <span>{record['Directories']?.join('/')}</span>;
    },
  },
  {
    dataIndex: 'CompareCode',
    title: '缺失对照的编码',
    visible: true,
    readonly: true,
    width: 200,
    // align: 'center',
  },
  {
    dataIndex: 'Code',
    title: '对照编码',
    visible: true,
    // align: 'center',
    width: 200,
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationMissingSelect
          module={entity?.['Module']}
          moduleGroup={entity?.['ModuleGroup']}
          onChange={(value) => {
            Emitter.emit(
              [
                ConfigurationEvents.MISSING_CODE_SELECT,
                `${ConfigurationEvents.MISSING_CODE_SELECT}#${entity?.TodoId}`,
              ],
              {
                id: entity?.TodoId,
                values: value,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'Name',
    title: '对照名称',
    visible: true,
    // align: 'center',
    width: 200,
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationReadonlyItem
          eventName={`${ConfigurationEvents.MISSING_CODE_SELECT}#${entity?.TodoId}`}
          nameKey={'Name'}
        />
      );
    },
  },
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 120,
    render: (node, record, index, action) => {
      return (
        <div className={'dictionary-item-operation'}>
          <a
            className={'operation-item'}
            onClick={() => {
              action?.startEditable?.(record.TodoId);
            }}
          >
            解决缺失
          </a>
          <Popconfirm
            title="确定删除？"
            placement={'top'}
            getPopupContainer={(triggerNode) =>
              document.getElementById('base-configuration-missing-table')
            }
            onConfirm={async () => {
              Emitter.emit(ConfigurationEvents.MISSING_DELETE, record);
            }}
          >
            <a className={'operation-item'}>删除缺失</a>
          </Popconfirm>
        </div>
      );
    },
  },
];

export const dmrOperSetColumns = [
  {
    dataIndex: 'RelatedDepts',
    render: (text, record) => {
      return text && text?.length > 0 ? (
        <>
          {[text]?.flat(1)?.map((item) => (
            <Tag key={item} style={{ marginBottom: '8px' }}>
              {item}
            </Tag>
          ))}
        </>
      ) : (
        <>-</>
      );
    },
  },
  {
    dataIndex: 'RelatedOperCodes',
    width: 500,
    visible: false,
    render: (text, record) => {
      return text && text?.length > 0 ? (
        <>
          {[text]?.flat(1)?.map((item) => (
            <Tag key={item} style={{ marginBottom: '8px' }}>
              {item}
            </Tag>
          ))}
        </>
      ) : (
        <>-</>
      );
    },
  },
  {
    dataIndex: 'IsValid',
    align: 'center',
    width: 60,
    render: (node, record, index, action) => {
      return <span>{record['IsValid'] ? '是' : '否'}</span>;
    },
  },
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 70,
    render: (node, record, index, action) => (
      <Space>
        <IconBtn
          key="edit"
          type="edit"
          onClick={() => {
            Emitter.emit(ConfigurationEvents.OPER_SET_EDIT, record);
          }}
        />
        <IconBtn
          key="delete"
          type="delete"
          openPop
          popOnConfirm={() => {
            Emitter.emit(ConfigurationEvents.OPER_SET_DELETE, record);
          }}
        />
      </Space>
    ),
  },
];

export const dmrCliDeptsDictColumns = [
  {
    dataIndex: 'index',
    title: '序号',
    fixed: 'left',
    visible: true,
    width: 80,
    align: 'center',
    readonly: true,
    order: Number.MIN_VALUE,
    render: (node, record, index) => {
      return <span>{index + 1}</span>;
    },
  },
  {
    dataIndex: 'Code',
    title: '科室编码',
    fixed: 'left',
    width: 150,
    readonly: true,
    // align: 'center',
    filterType: 'search',
  },
  {
    dataIndex: 'Name',
    title: '科室名称',
    // align: 'center',
    readonly: true,
    width: 250,
    fixed: 'left',
    filterType: 'search',
  },
  {
    dataIndex: 'InsurCode',
    width: 150,
    // align: 'center',
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationCliDeptsSelect
          type={'Insur'}
          formKeys={{
            InsurName: 'Name',
            InsurCode: 'Code',
          }}
          parentId={'operation-dictionary-table'}
          onCliDeptsSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.ESCALATE_INSURANCE_DEPARTMENT_SELECT}`,
                `${ConfigurationEvents.ESCALATE_INSURANCE_DEPARTMENT_SELECT_NAME}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'InsurName',
    // align: 'center',
    width: 250,
    readonly: true,
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.ESCALATE_INSURANCE_DEPARTMENT_SELECT_NAME}#${entity?.id}`}
        nameKey={'InsurName'}
      />
    ),
  },
  {
    dataIndex: 'HqmsCode',
    width: 150,
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationCliDeptsSelect
          type={'Hqms'}
          formKeys={{
            HqmsName: 'Name',
            HqmsCode: 'Code',
          }}
          parentId={'operation-dictionary-table'}
          onCliDeptsSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.ESCALATE_HQMS_DEPARTMENT_SELECT}`,
                `${ConfigurationEvents.ESCALATE_HQMS_DEPARTMENT_SELECT_NAME}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'HqmsName',
    width: 250,
    readonly: true,
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.ESCALATE_HQMS_DEPARTMENT_SELECT_NAME}#${entity?.id}`}
        nameKey={'HqmsName'}
      />
    ),
  },
  {
    dataIndex: 'WtCode',
    width: 150,
    filterType: 'search',
    renderFormItem: ({ index, entity }) => {
      return (
        <ConfigurationCliDeptsSelect
          type={'Wt'}
          formKeys={{
            WtName: 'Name',
            WtCode: 'Code',
          }}
          parentId={'operation-dictionary-table'}
          onCliDeptsSelect={(values) => {
            Emitter.emit(
              [
                `${ConfigurationEvents.ESCALATE_WT_DEPARTMENT_SELECT}`,
                `${ConfigurationEvents.ESCALATE_WT_DEPARTMENT_SELECT_NAME}#${entity?.id}`,
              ],
              {
                id: entity?.id,
                values: values,
                index: index,
              },
            );
          }}
        />
      );
    },
  },
  {
    dataIndex: 'WtName',
    width: 250,
    readonly: true,
    filterType: 'search',
    renderFormItem: ({ entity }) => (
      <ConfigurationReadonlyItem
        eventName={`${ConfigurationEvents.ESCALATE_WT_DEPARTMENT_SELECT_NAME}#${entity?.id}`}
        nameKey={'WtName'}
      />
    ),
  },
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 100,
    render: (node, record, index, action) => (
      <IconBtn
        key="edit"
        type="edit"
        onClick={() => {
          action?.startEditable?.(record.id);
        }}
      />
    ),
  },
];

export const operCategoryColumns = [...operOperations];
export interface ConfigurationFormBaseProps {
  value?: any;
  onChange?: (value: any) => void;
}
export const IcdeCategoryLevelSelect = (props: ConfigurationFormBaseProps) => {
  const { globalState } = useModel('@@qiankunStateForSlave');
  return (
    <UniSelect
      dataSource={globalState?.dictData?.['CategoryLevel']}
      placeholder="请选择分类"
      showSearch
      value={props?.value}
      optionNameKey={'Name'}
      optionValueKey={'Code'}
      allowClear={false}
      onChange={props?.onChange}
    />
  );
};

export const icdeCategoryColumns = (dictData) => [
  {
    dataIndex: 'CategoryLevel',
    width: 60,
    align: 'center',
    order: 1,
    renderFormItem: () => <IcdeCategoryLevelSelect />,
  },
  {
    dataIndex: 'IcdeCode',
    width: 100,
  },
  {
    dataIndex: 'IcdeName',
    width: 200,
  },
  {
    dataIndex: 'MainChapter',
    width: 200,
  },
  {
    dataIndex: 'SubChapter',
    width: 200,
  },
  {
    dataIndex: 'SubSubChapter',
    width: 200,
  },
  {
    dataIndex: 'Description',
    width: 200,
  },
  {
    dataIndex: 'Excludes',
    width: 150,
  },
  {
    dataIndex: 'Includes',
    width: 150,
  },
  {
    dataIndex: 'operation',
    title: '操作',
    valueType: 'option',
    visible: true,
    align: 'center',
    order: Number.MAX_VALUE,
    fixed: 'right',
    width: 100,
    render: (node, record, index, action) => (
      <>
        <Space size={'middle'}>
          <IconBtn
            key="edit"
            type="edit"
            onClick={() => {
              action?.startEditable?.(record.itemId);
            }}
          />
          <IconBtn
            key="delete"
            type="delete"
            openPop={true}
            popOnConfirm={() => {
              Emitter.emit(ConfigurationEvents.DMR_ICDE_DELETE, {
                index,
                record,
              });
            }}
          />
        </Space>
      </>
    ),
  },
];
