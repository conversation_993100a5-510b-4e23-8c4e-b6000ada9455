import { dmrCliDeptsDictColumns } from '@/pages/configuration/base/columns';
import { Button, Form, Modal, TableProps, Card, Space, Divider } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useRequest, useSelector } from 'umi';
import { RespVO, TableColumns, TableResp } from '@uni/commons/src/interfaces';
import { uniCommonService } from '@uni/services/src';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import './index.less';
import { EscalateCliDeptItem } from '@/pages/configuration/escalate/interfaces';
import { v4 as uuidv4 } from 'uuid';
import UniEditableTable from '@uni/components/src/table/edittable';
import { Emitter } from '@uni/utils/src/emitter';
import { ConfigurationEvents } from '@/pages/configuration/constants';
import { typesProcessor } from '@/pages/configuration/escalate/icde/processor';
import ConfigExcelTemplateHandler from '@/components/configExcelTemplateHandler';
import { TableColumnEditButton } from '@uni/components/src/table/column-edit/index';

const moduleGroup = 'Dmr';

const EscalateDepartmentDictionary = () => {
  const ref = useRef<any>();
  const [form] = Form.useForm();
  const dictData = useSelector((state) => state?.uniDict?.dictData?.Dmr);
  const [
    departmentDictionaryTableDataSource,
    setDepartmentDictionaryTableDataSource,
  ] = useState([]);

  const [departmentDictionaryColumns, setDepartmentDictionaryColumns] =
    useState([]);

  const [frontPagination, setFrontPagination] = useState({
    current: 1,
    pageSize: 10,
    pageSizeOptions: ['10', '20'],
  });
  // 前端分页OnChange
  const frontTableOnChange: TableProps<any>['onChange'] = (pagi) => {
    setFrontPagination({
      ...frontPagination,
      current: pagi.current,
      pageSize: pagi.pageSize,
    });
  };

  const [editableColumnKeys, setEditableColumnKeys] = useState<React.Key[]>([]);

  useEffect(() => {
    departmentConfigurationColumnsReq();
    departmentDictionaryReq();
  }, []);

  useEffect(() => {
    Emitter.onMultiple(
      [
        ConfigurationEvents.ESCALATE_INSURANCE_DEPARTMENT_SELECT,
        ConfigurationEvents.ESCALATE_HQMS_DEPARTMENT_SELECT,
        ConfigurationEvents.ESCALATE_WT_DEPARTMENT_SELECT,
      ],
      (data) => {
        let currentFormValue = form.getFieldsValue()?.[data?.id];
        if (currentFormValue) {
          // set form value
          Object.keys(data?.values)?.forEach((key) => {
            currentFormValue[key] = data?.values?.[key];
          });
          form.setFieldValue(data?.id, currentFormValue);
        }
      },
    );

    return () => {
      Emitter.offMultiple([
        ConfigurationEvents.ESCALATE_INSURANCE_DEPARTMENT_SELECT,
        ConfigurationEvents.ESCALATE_HQMS_DEPARTMENT_SELECT,
        ConfigurationEvents.ESCALATE_WT_DEPARTMENT_SELECT,
      ]);
    };
  }, [departmentDictionaryTableDataSource]);

  const onCliDeptItemSave = (rowKey, data) => {
    // table data
    let currentRowData = departmentDictionaryTableDataSource?.find(
      (item) => item?.HierarchyId === data?.HierarchyId,
    );
    if (currentRowData) {
      Object.keys(data)?.forEach((key) => {
        currentRowData[key] = data?.[key];
      });
    }

    setDepartmentDictionaryTableDataSource(
      departmentDictionaryTableDataSource?.slice(),
    );
  };

  const { loading: departmentDictionaryLoading, run: departmentDictionaryReq } =
    useRequest(
      () => {
        return uniCommonService(
          'Api/Sys/CodeSys/GetHierarchyCliDeptAllCompares',
          {
            method: 'POST',
            data: {},
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<EscalateCliDeptItem[]>) => {
          if (response.code === 0 && response?.statusCode === 200) {
            let tableDataSource = response?.data?.slice();
            setDepartmentDictionaryTableDataSource(
              tableDataSource.map((record) => {
                record['Types'] = typesProcessor(record);
                record['id'] = uuidv4();

                return record;
              }),
            );
          } else {
            setDepartmentDictionaryTableDataSource([]);
          }
        },
      },
    );

  const { run: departmentConfigurationColumnsReq } = useRequest(
    () => {
      return uniCommonService(
        'Api/Sys/CodeSys/GetHierarchyCliDeptAllCompares',
        {
          method: 'POST',
          headers: {
            'Retrieve-Column-Definitions': 1,
          },
        },
      );
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setDepartmentDictionaryColumns(
            tableColumnBaseProcessor(
              dmrCliDeptsDictColumns,
              response?.data?.Columns,
            ),
          );
        } else {
          setDepartmentDictionaryColumns([]);
        }
      },
    },
  );

  const { loading: departmentUpsertLoading, run: departmentUpsertReq } =
    useRequest(
      (values) => {
        let data = {};

        data = {
          ...values,
        };

        return uniCommonService(
          'Api/Sys/CodeSys/UpdateHierarchyCliDeptAllCompare',
          {
            method: 'POST',
            data: data,
          },
        );
      },
      {
        manual: true,
        formatResult: (response: RespVO<number>) => {
          return response;
        },
      },
    );

  return (
    <>
      <Card
        title="科室对照列表"
        extra={
          <Space>
            <ConfigExcelTemplateHandler
              downloadTemplateApiObj={{
                apiUrl:
                  'Api/Sys/CodeSys/GetHierarchyCliDeptAllCompareExcelTemplate',
              }}
              downloadPostData={{
                moduleGroup,
                exportName: '科室列表',
              }}
              uploadXlsxApiObj={{
                apiUrl:
                  'Api/Sys/CodeSys/UploadHierarchyCliDeptAllCompareExcelFile',
                onSuccess: () => {
                  departmentDictionaryReq();
                },
              }}
              uploadPostData={{
                moduleGroup,
              }}
            />
            <Divider type="vertical" />
            <TableColumnEditButton
              {...{
                columnInterfaceUrl:
                  'Api/Sys/CodeSys/GetHierarchyCliDeptAllCompares',
                onTableRowSaveSuccess: (columns) => {
                  setDepartmentDictionaryColumns(
                    tableColumnBaseProcessor(dmrCliDeptsDictColumns, columns),
                  );
                },
              }}
            />
          </Space>
        }
      >
        <UniEditableTable
          actionRef={ref}
          id={`department-dictionary-table`}
          className={'escalate-department-dictionary-table'}
          rowKey={'id'}
          scroll={{
            x: 'max-content',
            y:
              document.getElementById('content')?.offsetHeight -
              20 -
              40 -
              32 -
              32 -
              28,
          }}
          dictionaryData={dictData}
          widthDetectAfterDictionary
          bordered={true}
          loading={departmentDictionaryLoading || departmentUpsertLoading}
          columns={departmentDictionaryColumns}
          value={departmentDictionaryTableDataSource}
          clickable={false}
          pagination={frontPagination}
          onTableChange={frontTableOnChange}
          recordCreatorProps={false}
          editable={{
            form: form,
            type: 'multiple',
            editableKeys: editableColumnKeys,
            onSave: async (rowKey, data, row) => {
              let departmentUpsertResponse = await departmentUpsertReq(data);
              if (
                departmentUpsertResponse?.code === 0 &&
                departmentUpsertResponse?.statusCode === 200
              ) {
                onCliDeptItemSave(rowKey, data);
              }
            },
            actionRender: (row, config, defaultDoms) => {
              return [defaultDoms.save, defaultDoms.cancel];
            },
            onChange: setEditableColumnKeys,
          }}
        />
      </Card>
    </>
  );
};

export default EscalateDepartmentDictionary;
