import React, { useEffect, useState } from 'react';
import './index.less';
import { useRequest } from 'umi';
import dayjs from 'dayjs';
import {
  isEmptyValues,
  valueNullOrUndefinedReturnDash,
  valueNullOrUndefinedReturnDashWithDictionaryModule,
} from '@uni/utils/src/utils';
import { uniCommonService } from '@uni/services/src';
import {
  RespVO,
  TableCardResp,
  TableColumns,
} from '@uni/commons/src/interfaces';
import { DmrSearchTableItem } from '@/pages/dmr/components/dmr-search/interface';
import { Emitter, EventConstant } from '@uni/utils/src/emitter';
import { DmrEventConstant } from '@/utils/constants';
import Marquee from '@/pages/dmr/components/dmr-footer/marquee/Marquee';
import { tableColumnBaseProcessor } from '@uni/utils/src/tableColumnProcessor';
import { dmrSearchColumns } from '@/pages/dmr/columns';
import { useModel } from '@@/plugin-model/useModel';

const columns = [
  {
    dataIndex: 'PatNo',
    title: '住院号',
    visible: true,
  },
  {
    dataIndex: 'PatAdmNo',
    title: '病案号',
    visible: true,
  },
  {
    dataIndex: 'PatName',
    title: '姓名',
    visible: true,
  },
];

const DmrFooter = (props: any) => {
  const [registeredDmrItem, setRegisteredDmrItem] = useState(undefined);
  const [registeredTotal, setRegisteredTotal] = useState(0);
  const [registerItemColumns, setRegisterItemColumns] = useState([]);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  useEffect(() => {
    if (!isEmptyValues(globalState?.userInfo?.EmployeeCode)) {
      dmrCheckedSearchReq();
    }

    Emitter.on(DmrEventConstant.DMR_FOOTER_GET_LATEST_RECORD, () => {
      if (!isEmptyValues(globalState?.userInfo?.EmployeeCode)) {
        dmrCheckedSearchReq();
      }
    });

    return () => {
      Emitter.off(DmrEventConstant.DMR_FOOTER_GET_LATEST_RECORD);
    };
  }, [globalState?.userInfo]);

  const { loading: dmrCheckedSearchLoading, run: dmrCheckedSearchReq } =
    useRequest(
      () => {
        let data = {
          DtParam: {
            Draw: 1,
            Start: 0,
            Length: 1,
            order: [{ column: 0, dir: 'desc' }],
            columns: [{ Data: 'CodeTime' }],
          },
          Coder: [globalState?.userInfo?.EmployeeCode],
          UseRegistDate: true,
          // CodeSdate: dayjs("2024-02-23").format('YYYY-MM-DD 00:00:00'),
          // CodeEdate: dayjs("2024-02-23").format('YYYY-MM-DD 23:59:59'),
          CodeSdate: dayjs().format('YYYY-MM-DD 00:00:00'),
          CodeEdate: dayjs().format('YYYY-MM-DD 23:59:59'),
        };

        return uniCommonService('Api/Dmr/DmrDataQuery/GetPendingCards', {
          method: 'POST',
          data: data,
        });
      },
      {
        manual: true,
        formatResult: (
          response: RespVO<TableCardResp<any, DmrSearchTableItem>>,
        ) => {
          if (response.code === 0 && response?.statusCode === 200) {
            setRegisteredDmrItem(response?.data?.data?.at(0));
            setRegisteredTotal(response?.data?.recordsTotal ?? 0);
          } else {
            setRegisteredDmrItem(undefined);
          }
        },
      },
    );

  const { run: dmrCareColumnsReq } = useRequest(
    () => {
      return uniCommonService('Api/Dmr/DmrDataQuery/GetPendingCards', {
        method: 'POST',
        headers: {
          'Retrieve-Column-Definitions': 1,
        },
      });
    },
    {
      manual: true,
      formatResult: (response: RespVO<TableColumns>) => {
        if (response.code === 0) {
          setRegisterItemColumns(
            tableColumnBaseProcessor([], response?.data?.Columns),
          );
        } else {
          setRegisterItemColumns([]);
        }
      },
    },
  );

  const lastRegisterDmrItemLabelProcessor = () => {
    // let visibleRegisterItemColumns = registerItemColumns?.filter(
    //   (item) => item?.visible === true,
    // );
    let visibleRegisterItemColumns = columns;
    let lastRegisteredLabel = visibleRegisterItemColumns?.map(
      (columnItem, index) => {
        if (!isEmptyValues(registeredDmrItem?.[columnItem?.dataIndex])) {
          let itemLabel = valueNullOrUndefinedReturnDashWithDictionaryModule(
            registeredDmrItem,
            columnItem,
            globalState?.dictData,
          );
          if (itemLabel) {
            return (
              <span
                className={'label-item'}
                // className={`${
                //   index === visibleRegisterItemColumns?.length - 1
                //     ? 'label-last-item'
                //     : 'label-item'
                // }`}
              >
                {columnItem?.title}：{itemLabel}
                {index === visibleRegisterItemColumns?.length - 1 ? '。' : '，'}
              </span>
            );
          }
        }

        return null;
      },
    );

    if (!isEmptyValues(registeredDmrItem)) {
      // shift一个上一份录入的病案号
      lastRegisteredLabel.unshift(
        <span className="label-item">上一份录入病案：</span>,
      );
      // 追加 本人今天一共录入多少病案
      lastRegisteredLabel.push(
        <span className="label-last-item">
          今天一共录入 {registeredTotal} 份病案。
        </span>,
      );
    }

    return lastRegisteredLabel;
  };

  return (
    <div className={'dmr-footer-container'}>
      <Marquee
        autoFill={true}
        pauseOnHover={true}
        direction="left"
        speed={30}
        gradient={true}
      >
        {!isEmptyValues(registeredDmrItem) ? (
          <div className={'flex-row-center'}>
            {lastRegisterDmrItemLabelProcessor()}
          </div>
        ) : (
          <></>
        )}
      </Marquee>
    </div>
  );
};

export default DmrFooter;
