import { isEmptyValues } from '@uni/utils/src/utils';
import cloneDeep from 'lodash/cloneDeep';
import mergeWith from 'lodash/mergeWith';
import pick from 'lodash/pick';

export const filterDuplicatedOperCodesOnComboSelect = (
  tableData: any[],
  comboData: any[],
) => {
  return comboData.filter((comboDataItem: any) => {
    return (
      tableData?.find((item) => comboDataItem?.OperCode === item?.OperCode) ===
      undefined
    );
  });
};

const extraOperationComboRules =
  (window as any).externalConfig?.['dmr']?.extraOperationComboRules ?? [];

export class OperationComboRules {
  tableData: any[];
  comboItem: any;
  comboOperationCodes: string[];
  triggeredRecordIndex: number;
  copyFormKeys: string[];

  operationComboRules = ['CurrentItem', 'HasComboItem', 'FirstItem'];

  constructor(
    comboOperationCodes: string[],
    triggeredRecordIndex: number,
    copyFormKeys: string[],
    excludeCopyKeys?: string[],
  ) {
    this.comboOperationCodes = comboOperationCodes;
    this.triggeredRecordIndex = triggeredRecordIndex;
    this.copyFormKeys = copyFormKeys?.filter((item) => {
      return !excludeCopyKeys?.includes(item);
    });
  }

  sourceFirstMergeCustomizer = (objValue, srcValue) => {
    if (!isEmptyValues(srcValue)) {
      return srcValue;
    }

    return objValue;
  };

  objectFirstMergeCustomizer = (objValue, srcValue) => {
    if (!isEmptyValues(objValue)) {
      return objValue;
    }

    return srcValue;
  };

  // TODO 3套 规则
  #comboSelectOtherFormKeysRuleCurrentItem = () => {
    // 当且仅当 有的时候
    if (this?.triggeredRecordIndex !== -1) {
      let currentTriggerRecord = this.tableData?.at(this.triggeredRecordIndex);
      mergeWith(
        this.comboItem,
        pick(currentTriggerRecord, this.copyFormKeys),
        this.sourceFirstMergeCustomizer,
      );
    }
  };

  #comboSelectOtherFormKeysRuleHasComboItem = () => {
    let firstItemInComboOperCodes = this.tableData?.find((tableDataItem) =>
      this.comboOperationCodes?.includes(tableDataItem.OperCode),
    );

    if (!isEmptyValues(firstItemInComboOperCodes)) {
      mergeWith(
        this.comboItem,
        pick(firstItemInComboOperCodes, this.copyFormKeys),
        this.objectFirstMergeCustomizer,
      );
    }
  };

  #comboSelectOtherFormKeysRuleFirstItem = () => {
    let firstItemInTableData = this.tableData?.at(0);

    if (!isEmptyValues(firstItemInTableData)) {
      mergeWith(
        this.comboItem,
        pick(firstItemInTableData, this.copyFormKeys),
        this.objectFirstMergeCustomizer,
      );
    }
  };

  operationComboRuleKeyMap = {
    method: this.#comboSelectOtherFormKeysRuleCurrentItem,
    HasComboItem: this.#comboSelectOtherFormKeysRuleHasComboItem,
    FirstItem: this.#comboSelectOtherFormKeysRuleFirstItem,
  };

  comboSelectedItemsProcess = (tableData: any[], comboItem: any) => {
    this.comboItem = cloneDeep(comboItem);
    this.tableData = tableData;

    let rules = this.operationComboRules;
    if (!isEmptyValues(extraOperationComboRules)) {
      rules = extraOperationComboRules;
    }

    rules?.forEach((ruleKey: string) => {
      this.operationComboRuleKeyMap?.[ruleKey]?.(tableData);
    });

    return this.comboItem;
  };
}
