import { Button, message, Modal } from 'antd';
import { uniCommonService } from '@uni/services/src';
import { useModel } from '@@/plugin-model/useModel';
import { useRequest } from 'ahooks';
import { RespVO } from '@uni/commons/src/interfaces';
import { isEmptyValues } from '@uni/utils/src/utils';

export default function SyncIcdeButton({
  type,
  refreshTable,
  disabled,
  editableFinalValue,
}: {
  type: 'byDay' | 'byDept';
  refreshTable: () => void;
  disabled?: boolean;
  editableFinalValue?: any;
}) {
  const isShowSyncIcdeBtn =
    (window as any).externalConfig?.['operationalDataManagement']
      ?.isShowSyncIcdeBtn ?? false;
  const { globalState } = useModel('@@qiankunStateFromMaster');
  let { searchParams } = globalState;

  const { loading: loading, run: SyncData } = useRequest(
    () => {
      const params = {
        Sdate:
          type === 'byDay'
            ? searchParams?.singleDate
            : searchParams?.dateRange?.at(0),
        Edate:
          type === 'byDay'
            ? searchParams?.singleDate
            : searchParams?.dateRange?.at(1),
        HospCode: searchParams?.hospCodes,
        ...(type === 'byDept' && { DeptCode: searchParams.dynDept }),
      };

      return uniCommonService('Api/Dyn-ddr/InpatientAmts/SyncIcdeOutcome ', {
        method: 'POST',
        data: params,
      });
    },
    {
      manual: true,
      onSuccess: (response: RespVO<any>) => {
        if (response?.code === 0 && response?.statusCode === 200) {
          message.success('同步成功');
          refreshTable();
        }
      },
    },
  );
  // 判断按钮是否应该禁用
  const isButtonDisabled = () => {
    if (type === 'byDay') {
      return (
        isEmptyValues(searchParams?.hospCodes) ||
        searchParams?.hospCodes?.length === 0 ||
        disabled
      );
    } else if (type === 'byDept') {
      return (
        isEmptyValues(searchParams?.dynDept) ||
        isEmptyValues(searchParams?.hospCodes) ||
        searchParams?.hospCodes?.length === 0 ||
        disabled
      );
    }
    return true;
  };

  return (
    isShowSyncIcdeBtn && (
      <Button
        key="SyncIcdeOutcome"
        loading={loading}
        disabled={isButtonDisabled()}
        onClick={() => {
          if (editableFinalValue.some((e) => e.IsLocked)) {
            Modal.confirm({
              title: '本次查询结果包含已锁定数据，已锁定数据不做更新',
              onOk: () => {
                SyncData();
              },
              onCancel: () => {},
            });
          } else {
            SyncData();
          }
        }}
      >
        转归同步
      </Button>
    )
  );
}
