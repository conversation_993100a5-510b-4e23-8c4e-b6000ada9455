import { IGridItem } from '@/pages/dmr/interfaces';

export const inHospitalOthers: IGridItem[] = [
  {
    data: {
      prefix: '',
      key: 'AdmissionPathAndHospital',
      desc: '',
      suffix: '',
      component: 'AdmissionPathType',
      props: {
        className: 'border-none',
        style: {
          border: 'none',
        },
        modelDataKey: 'RYTJ',
        modelDataGroup: 'Dmr',
        hospInputType: 'text',
      },
    },
    h: 1,
    w: 16,
  },
  {
    data: {
      prefix: '病人来源',
      key: 'PatFrom',
      desc: '',
      suffix: '',
      component: 'AutoSelect',
      props: {
        formKey: 'PatFrom',
        modelDataKey: 'PatFrom',
        modelDataGroup: 'Dmr',
        className: '',
        formItemStyle: { width: '100%' },
      },
    },
    visible: false,
    w: 4,
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '入院确诊时间',
      key: 'CnfmDate',
      desc: '',
      suffix: '',
      // component: 'DatePicker',
      component: 'DateSelect',
      props: {
        formKey: 'CnfmDate',
        type: 'compact',
        style: {
          width: '100%',
        },
        className: 'border-none',
        datePicker: false,
        showHours: true,
        showSeconds: true,
      },
    },
    w: 4,
    md: {
      w: 8,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '是否日间手术',
      key: 'IsDaySurgery',
      desc: '',
      suffix: '',
      component: 'DmrSelect',
      props: {
        formKey: 'IsDaySurgery',
        modelDataKey: 'IsDaySurgery',
        modelDataGroup: 'Dmr',
        placeholder: '请选择',
        optionNameKey: 'Name',
      },
    },
    w: 4,
    xxs: {
      w: 10,
    },
  },
];

export const inHospital: IGridItem[] = [
  {
    data: {
      prefix: '入院时间',
      key: 'InDate',
      desc: '',
      suffix: '',
      // component: 'DatePicker',
      component: 'DateSelect',
      props: {
        formKey: 'InDate',
        type: 'compact',
        style: {
          width: '100%',
        },
        className: 'border-none',
        datePicker: false,
        showHours: true,
        showSeconds: true,
        calculateItems: [
          {
            needCalculateFormKey: 'InPeriod',
            calculateFromFormKeys: ['InDate', 'OutDate'],
            dateCalculateUnits: 'day',
          },
          {
            needCalculateFormKey: 'PatAge',
            calculateFromFormKeys: ['PatBirth', 'InDate'],
            dateCalculateUnits: 'year',
          },
        ],
      },
    },
    w: 4,
    md: {
      w: 8,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '入院科别',
      key: 'InDept',
      desc: '',
      suffix: '',
      component: 'DmrSelect',
      props: {
        formKey: 'InDept',
        className: 'department-container',
        modelDataKey: 'CliDepts',
        placeholder: '请选择',
        optionNameKey: 'Name',
      },
    },
    // offsetX: 1,
    w: 4,
    md: {
      w: 8,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '病房',
      key: 'InWard',
      desc: '',
      suffix: '',
      component: 'Input',
      props: {
        bordered: false,
      },
    },
    w: 4,
    md: {
      w: 8,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '转科科别',
      key: 'TransDept',
      desc: '',
      suffix: '',
      component: 'DepartmentTransfer',
      props: {
        formKey: 'TransDept',
      },
    },
    w: 6,
    md: {
      w: 8,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
];

export const inHospitalIcde: IGridItem[] = [
  {
    data: {
      prefix: '入院情况',
      key: 'InCond',
      desc: '',
      suffix: '',
      component: 'AutoSelect',
      props: {
        key: 'InCond',
        formKey: 'InCond',
        modelDataKey: 'InCond',
        modelDataGroup: 'Dmr',
        className: '',
        formItemStyle: { width: '100%' },
      },
    },
    w: 4,
    md: {
      w: 4,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 12,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '入院诊断编码',
      key: 'IcdeAdmsItem',
      desc: '',
      suffix: '',
      component: 'IcdeSelect',
      props: {
        componentId: 'IcdeAdmsIcdeCode',
        selectFormKey: 'IcdeAdmsIcdeCode',
        itemKey: 'IcdeAdmsItem',
        formKeys: {
          IcdeAdmsIcdeName: 'Name',
          IcdeAdmsIcdeCode: 'Code',
        },
        parentId: 'dmr-content-container',
        icdeSelectType: 'IsAdm',
      },
    },
    w: 4,
    md: {
      w: 5,
    },
    sm: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
  {
    data: {
      prefix: '名称',
      key: 'IcdeAdmsIcdeName',
      desc: '',
      suffix: '',
      component: 'Input',
      props: {
        bordered: false,
        disabled: true,
      },
    },
    w: 8,
    md: {
      w: 7,
    },
    xs: {
      w: 6,
    },
    xxs: {
      w: 10,
    },
  },
];
