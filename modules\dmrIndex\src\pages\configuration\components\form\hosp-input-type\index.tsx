// import './index.less';
import { Form, Input } from 'antd';
import {
  ProForm,
  ProFormSelect,
  ProFormText,
  ProFormDependency,
  ProFormRadio,
} from '@uni/components/src/pro-form';
import React, { useEffect } from 'react';
import { useModel } from 'umi';

const HospInutType = (props: any) => {
  const form = Form.useFormInstance();

  const hospInputType = Form.useWatch('data.props.hospInputType');

  const { globalState } = useModel('@@qiankunStateFromMaster');

  console.log('values', form.getFieldsValue());

  return (
    <div className={'hosp-input-type-container'}>
      <ProForm.Group title={'机构名称 配置'}>
        <ProFormRadio.Group
          name={['data.props.hospInputType']}
          label="用户输入类型"
          initialValue={hospInputType || 'text'}
          options={[
            { label: '后端查询', value: 'search' },
            { label: '手动输入', value: 'text' },
          ]}
        />
      </ProForm.Group>
    </div>
  );
};

export default HospInutType;
