/**
 * 病案首页 入院途径和机构名称 联动组件
 */
import React, { useEffect, useState } from 'react';
import './index.less';
import { Col, Form, Row, Spin, Tooltip } from 'antd';
import { AutoSelect } from '@uni/grid/src/components/auto-select';
import { RequireMark } from '@uni/grid/src/components/require-mark';
import { uniCommonService } from '@uni/services/src';
import { RespVO } from '@uni/commons/src/interfaces';
import { useDebounceFn } from 'ahooks';
import { getSelectorDropdownContainerNode } from '@uni/grid/src/utils';
import { UniAntdSelect, UniInput } from '@uni/components/src';
// @ts-ignore
import { useModel } from '@@/plugin-model/useModel';

const inputFocusNotSelectAll =
  (window as any).externalConfig?.['dmr']?.inputFocusNotSelectAll ?? false;

const tableSelectorDropdownHeight = (window as any).externalConfig?.['dmr']
  ?.tableSelectorDropdownHeight;

export interface OtherHospitalItem {
  HospCode: string;
  HospName: string;
  InsurOrganizationCode?: string;
  InsurOrganizationName?: string;
  IsValid: boolean;
}

interface AdmissionPathTypeProps {
  form: any;
  value?: string;
  onChange?: (value: string) => void;
  modelDataGroup?: string;
  modelDataKey?: string;
  hospInputType?: string;
}

export const AdmissionPathType = (props: AdmissionPathTypeProps) => {
  const [admissionPathItems, setAdmissionPathItems] = React.useState<any[]>([]);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [errorTooltipOpen, setErrorTooltipOpen] = useState(false);

  const { globalState } = useModel('@@qiankunStateFromMaster');

  useEffect(() => {
    if (props?.modelDataKey && props?.modelDataGroup) {
      setAdmissionPathItems(
        globalState?.dictData?.[props?.modelDataGroup]?.[props?.modelDataKey] ??
          [],
      );
    }
  }, [globalState]);

  console.log('AdmissionPathType', props);

  // 获取其他医疗机构数据源
  const getOtherHospitalDataSource = async (searchKeyword) => {
    if (!searchKeyword) {
      return;
    }

    setLoading(true);
    const data = {
      Keyword: searchKeyword?.trim(),
    };

    try {
      const response: RespVO<{
        Data: OtherHospitalItem[];
        RecordsTotal: number;
      }> = await uniCommonService('Api/MetaData/Search/OtherHospital', {
        params: data,
      });
      console.log('Api/MetaData/Search/OtherHospital', response);
      if (response?.code === 0 && response?.statusCode === 200) {
        setDataSource(
          response?.data?.Data?.filter((item) => item?.IsValid)?.map(
            (item) => ({
              ...item,
              key: item?.HospCode,
              value: item?.HospCode,
              label: `${item?.HospName || ''}`.trim(),
            }),
          ) || [],
        );
      }
    } catch (error) {
      console.error('获取其他医疗机构数据失败:', error);
      setDataSource([]);
    }

    setLoading(false);
  };

  const { run: getOtherHospitalDataSourceWithDebounce } = useDebounceFn(
    (keyword) => {
      getOtherHospitalDataSource(keyword);
    },
    {
      wait: 200,
    },
  );

  const onOtherHospitalSelectClear = () => {
    props?.form.setFieldValue('TransferFromHosp', '');
    // 清理状态
    setDataSource([]);
    setHasSearched(false);
    setErrorTooltipOpen(false);
  };

  const onOtherHospitalSelect = (value: string) => {
    const currentSelected = dataSource?.find((item) => item.value === value);

    if (currentSelected) {
      // 保存选中的机构名称到 TransferFromHosp 字段
      const selectedValue = currentSelected.HospName;
      props?.form.setFieldValue('TransferFromHosp', selectedValue);

      // 重置搜索状态
      // setDataSource([]);
      setErrorTooltipOpen(false);
      setHasSearched(false);
    }
  };

  // 监听入院途径字段的值变化，参照 SuffixItem 的写法
  const admissionPathValue = Form.useWatch('AdmissionPath', props?.form);
  console.log('onOtherHospitalSelect dataSource', dataSource);
  return (
    <div className={'admission-path-type-container'}>
      <div className={'admission-path-row-wrap'}>
        <label className={'prefix'}>
          <RequireMark />
          入院途径
        </label>
        <Form.Item name={'AdmissionPath'}>
          <AutoSelect
            className={`input-container primary-input form-content-item-container`}
            formItemId={`formItem#AdmissionPath`}
            dataSource={admissionPathItems}
            formKey={'AdmissionPath'}
            width={290}
            form={props?.form}
            value={props?.form?.getFieldValue('AdmissionPath')}
            onChange={(value) => {
              if (value !== '3') {
                props?.form.setFieldValue('TransferFromHosp', '');
              }
            }}
          />
        </Form.Item>
      </div>
      <div className={'transfer-hospital-item'}>
        <label className={'label'}>其他医疗机构 机构名称</label>
        <Form.Item
          name={'TransferFromHosp'}
          className={'form-content-item-container'}
          style={{ flex: 'auto' }}
        >
          {/* 根据hospInputType判断使用哪种输入组件 */}
          {!props?.hospInputType || props?.hospInputType === 'text' ? (
            // 使用普通的UniInput
            <UniInput
              id={`formItem#TransferFromHosp#Input`}
              className={`hospital-input`}
              disabled={admissionPathValue?.toString() !== '3'}
              bordered={false}
              value={props?.form?.getFieldValue('TransferFromHosp')}
              onChange={(e) => {
                props?.form?.setFieldValue('TransferFromHosp', e.target.value);
              }}
            />
          ) : (
            // 使用UniAntdSelect进行搜索选择
            <Tooltip
              open={errorTooltipOpen}
              color={'rgba(235, 87, 87, 0.85)'}
              title={'机构不存在，请检查后重新选择'}
            >
              <UniAntdSelect
                id={`formItem#TransferFromHosp#OtherHospitalSelect`}
                className={`hospital-select`}
                showSearch
                showArrow={false}
                allowClear={false}
                disabled={admissionPathValue?.toString() !== '3'}
                dropdownMatchSelectWidth={false}
                getPopupContainer={() => getSelectorDropdownContainerNode()}
                onInputKeyDown={(event) => {
                  if (hasSearched) {
                    if (event.key === 'Enter' && dataSource?.length === 0) {
                      setErrorTooltipOpen(true);
                      event.preventDefault();
                      event.stopPropagation();
                    }
                  }
                }}
                enterSwitch={true}
                contentEditable={!inputFocusNotSelectAll}
                listHeight={tableSelectorDropdownHeight}
                placeholder={
                  admissionPathValue?.toString() === '3'
                    ? '请选择转出医疗机构'
                    : '请先选择入院途径为"3"'
                }
                onSearch={(searchKeyword) => {
                  if (admissionPathValue?.toString() === '3') {
                    setHasSearched(true);
                    if (searchKeyword) {
                      getOtherHospitalDataSourceWithDebounce(searchKeyword);
                    } else {
                      setDataSource([]);
                    }
                  }
                }}
                filterOption={false}
                notFoundContent={loading ? <Spin size="small" /> : null}
                onBlur={() => {
                  setTimeout(() => {
                    setDataSource([]);
                    setHasSearched(false);
                    setErrorTooltipOpen(false);
                  }, 0);
                }}
                onClear={() => {
                  onOtherHospitalSelectClear();
                }}
                onSelect={(value) => {
                  onOtherHospitalSelect(value);
                }}
                value={props?.form?.getFieldValue('TransferFromHosp')}
                options={dataSource}
                dumbOnComposition={true}
                mousedownOptionOpen={false}
                doubleClickPopUp={false}
              />
            </Tooltip>
          )}
        </Form.Item>
      </div>
    </div>
  );
};

// 保留原有的 OtherHospitalSelect 组件以保持向后兼容
export interface OtherHospitalSelectProps {
  form?: any;
  dataIndex?: string;
  componentId: string;
  selectFormKey?: string;
  getPopupContainer?: (trigger) => HTMLElement;
  dropdownStyle?: React.CSSProperties;
  listHeight?: number;
  disabled?: boolean;
  recordId?: string;
  tableId?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

const OtherHospitalSelect = (props: OtherHospitalSelectProps) => {
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [errorTooltipOpen, setErrorTooltipOpen] = useState(false);

  // 获取其他医疗机构数据源
  const getOtherHospitalDataSource = async (searchKeyword) => {
    if (!searchKeyword) {
      return;
    }

    setLoading(true);
    const data = {
      Keyword: searchKeyword?.trim(),
    };

    try {
      const response: RespVO<OtherHospitalItem[]> = await uniCommonService(
        'Api/MetaData/Search/OtherHospital',
        {
          params: data,
        },
      );

      if (response?.code === 0) {
        if (response?.statusCode === 200) {
          setDataSource(
            response?.data
              ?.filter((item) => item?.IsValid)
              ?.map((item) => ({
                ...item,
                key: item?.HospCode,
                value: item?.HospCode,
                label: `${item?.HospName || ''}`.trim(),
              })) || [],
          );
        }
      }
    } catch (error) {
      console.error('获取其他医疗机构数据失败:', error);
      setDataSource([]);
    }

    setLoading(false);
  };

  const { run: getOtherHospitalDataSourceWithDebounce } = useDebounceFn(
    (keyword) => {
      getOtherHospitalDataSource(keyword);
    },
    {
      wait: 200,
    },
  );

  const onOtherHospitalSelectClear = () => {
    if (props?.form) {
      if (props?.recordId) {
        props?.form.setFieldValue([props?.recordId, 'TransferFromHosp'], '');
      } else {
        props?.form.setFieldValue('TransferFromHosp', '');
      }
    }

    // 清理状态
    setDataSource([]);
    setHasSearched(false);
    setErrorTooltipOpen(false);

    props?.onChange && props?.onChange('');
  };

  const onOtherHospitalSelect = (value: string) => {
    const currentSelected = dataSource?.find((item) => item.value === value);

    if (currentSelected) {
      // 保存选中的机构名称到 TransferFromHosp 字段
      const selectedValue = currentSelected.HospName;

      if (props?.form) {
        if (props?.recordId) {
          props?.form.setFieldValue(
            [props?.recordId, 'TransferFromHosp'],
            selectedValue,
          );
        } else {
          props?.form.setFieldValue('TransferFromHosp', selectedValue);
        }
      }

      props?.onChange && props?.onChange(selectedValue);

      // 重置搜索状态
      setDataSource([]);
      setErrorTooltipOpen(false);
      setHasSearched(false);
    }
  };

  return (
    <div className={'form-content-item-container'}>
      <Tooltip
        open={errorTooltipOpen}
        color={'rgba(235, 87, 87, 0.85)'}
        title={'机构不存在，请检查后重新选择'}
      >
        <Form.Item name={props?.selectFormKey}>
          <UniAntdSelect
            id={`formItem#${props?.componentId}#OtherHospitalSelect`}
            className={`select`}
            value={props?.value}
            showSearch
            showArrow={false}
            allowClear={false}
            disabled={props?.disabled}
            dropdownMatchSelectWidth={false}
            optionLabelProp={'value'}
            getPopupContainer={(trigger) =>
              (props.getPopupContainer && props?.getPopupContainer(trigger)) ||
              getSelectorDropdownContainerNode()
            }
            onInputKeyDown={(event) => {
              if (hasSearched) {
                if (event.key === 'Enter' && dataSource?.length === 0) {
                  setErrorTooltipOpen(true);
                  event.preventDefault();
                  event.stopPropagation();
                }
              }
            }}
            enterSwitch={true}
            contentEditable={!inputFocusNotSelectAll}
            dropdownStyle={props?.dropdownStyle || {}}
            listHeight={tableSelectorDropdownHeight ?? props?.listHeight}
            placeholder={props?.placeholder || '请选择转出医疗机构'}
            onSearch={(searchKeyword) => {
              setHasSearched(true);
              if (searchKeyword) {
                getOtherHospitalDataSourceWithDebounce(searchKeyword);
              } else {
                setDataSource([]);
              }
            }}
            filterOption={false}
            notFoundContent={loading ? <Spin size="small" /> : null}
            onBlur={() => {
              setTimeout(() => {
                setDataSource([]);
                setHasSearched(false);
                setErrorTooltipOpen(false);
              }, 0);
            }}
            onClear={() => {
              onOtherHospitalSelectClear();
            }}
            onSelect={(value) => {
              onOtherHospitalSelect(value);
            }}
            options={dataSource}
            dumbOnComposition={true}
            mousedownOptionOpen={false}
            doubleClickPopUp={false}
          />
        </Form.Item>
      </Tooltip>
    </div>
  );
};

export default OtherHospitalSelect;
