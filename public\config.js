const ExternalConfig = {
  // ************:20012
  // ip: '************',
  // port: '10012',
  // ip: '************',
  // port: '51019',
  ip: '************',
  port: '5181',
  // domain配置：此配置用于 服务器 被拆分部署在不同端口下使用。不要修改，让开发配置
  domain: {},

  // 通用配置
  common: {
    // 特殊Logo：用于 login / 顶部菜单栏右侧 显示 特殊Logo 一般是医院Logo
    externalLogo: '',
    // 系统标题：用于main / login / 侧边菜单栏顶部 显示系统标题
    title: '智慧医保DRG运营平台',
    // 顶部导航栏 右侧显示的医院名称 不填则不显示
    hospitalName: '',
    // 左侧菜单点击是否需要打开新标签页
    leftNewTab: false,
    // 顶部菜单点击是否需要打开新标签页
    headerNewTab: false,
    // 是否显示 chrome下载 按钮
    chromeDownload: true,
    // 是否显示 数据生图标 按钮：比如组合查询 统计 右上角）
    dataToCharts: true,
    // 全局搜索项调试用数据：**生产环境必为false**
    parameterDateMock: false,
    // 自定义默认搜索参数：一般主要用于 设定院区/科室/时间范围 等默认参数
    // 特别说明：dateRange 可以使用特殊string，可用string参数：today,tomorrow,yesterday,{current|prev|next}Date,{current|prev|next}Week,{current|prev|next}HalfMonth,{current|prev|next}Month,{current|prev|next}Quarter,{current|prev|next}Year. 尤其注意其中字母大小写
    customDefaultSearchParam: {
      dateRange: ['2020-01-01', '2024-03-31'],
    },

    // 知识库 按钮 显示与否
    wikiShow: true,
    // 医保知识库 按钮 显示与否
    wikiInsurShow: true,
    // 站内私信显隐与否
    personalMessageShow: true,
    // 显示“发送到”按钮与否
    showSendToBtn: true,
    // 搜索项查询按钮相关配置：当为false可不修改顶部搜索项 来再次刷新数据， 生产环境默认为false
    headerSearchForceModified: false,
    // 关闭标签页时清除token：当有多个标签页时 当且仅当关闭最后一个 会清除token (请不要随意修改为true)
    clearTokenOnClose: false,
    // 分页设置，用于设定全局分页的pageSizeOption：具体内部参数见 https://4x.ant.design/components/pagination-cn/
    paginationConfig: {
      pageSizeOptions: [10, 50, 100],
      defaultPageSize: 10,
    },
    // 首页登记/结算清单 自定义快捷键设置：请不要擅自修改，根据需求并联系开发修改
    customShortcuts: {},
    // 示踪页面的可自定义查询参数：这个默认是false 目前已知修水是true
    paramCustomizeEnable: false,
    // 默认落地页：可以通过维护→全局设置→菜单设置 来查看所有url，优先级比通过userInfo获取的低
    defaultLandingPage: '/qualityControl/main/details/type',
    // 全局的表格 zebra 颜色
    tableStripeConfig: {
      oddColor: '#f2f2f2',
      evenColor: '#ffffff',
    },
    // 整个系统统一的搜索输入框配置（无法对 首页&组合查询 生效）
    searchConfig: {
      // 下拉框
      select: {
        // 在搜索过滤后 选择数据时 是否要自动清空搜索内容（清空过滤机制）：false=不清空 true=清空
        autoClearSearchValue: false,
      },
    },
    // 是否需要检测用户浏览器的Chrome版本 并在低于推荐版本103时弹框提示
    minChromeVersion: false,
    // 启用动态菜单与否：当配置了dynamic时要想有效果，这个参数必须为true。目前已知 评审页面 KPI页面使用
    enableDynamicMenuItem: true,
    // highlight报表配置：**交给开发配置，请不要随意修改
    extraReportAppCodes: [
      {
        appCode: 'HospReportHighlight',
        routePrefix: '/statsAnalysis/report',
        parentRoute: '/statsAnalysis',
      },
      {
        appCode: 'HighlightChsDrgReport',
        routePrefix: '/chs/analysis',
        parentRoute: '/chs/analysis',
      },
      {
        appCode: 'HighlightDipReport',
        routePrefix: '/chs/dip',
        parentRoute: '/chs/dip',
      },
      {
        appCode: 'HighlightInsurReport',
        routePrefix: '/chs/main',
        parentRoute: '/chs/main',
      },
      {
        appCode: 'HighlightQualityExamineReport',
        routePrefix: '/dmr/examine',
        parentRoute: '/dmr/examine',
      },
    ],
  },

  // 动态菜单配置：要结合common.enableDynamicMenuItem 一同使用 **交给开发配置，请不要随意修改
  dynamic: {
    componentAlias: {
      DmrExamineReviewer: './pages/review/reviewer/index',
      DmrExamineReviewee: './pages/review/auditee/index',
      DmrExamineManagement: './pages/review/management/index',
      DmrExamineAnalysis: './pages/review/analysis/index',
    },
    headerAlias: {
      reviewDataAnalysis: ['DmrExamineAnalysis'],
    },
  },

  // 病案首页相关配置
  dmr: {
    // 是否更新首页布局：生产环境请严格设定为false (当且仅当首次部署设定为true，并于首页打开一次之后修改为false)
    updateLayout: false,
    // 是否为假的病案调阅：生产环境严格为false
    fakeMedicalRecord: false,
    // 不修改也可保存
    noEditSave: true,
    // 保存时顺便质控与否
    checkOnSave: false,
    // 保存时顺便病案签收与否：https://devops.aliyun.com/projex/req/DMRDEV-1165# 《登记界面增加按钮支持病案签收》，不填默认 false
    signinOnSave: true,
    // input聚焦时是否选中全部：false=input聚焦时选中全部；true=input聚焦时不会选中全部
    inputFocusNotSelectAll: false,
    // 表格新增按钮配置：true=点击表格最后一行之后“新增”文字才会新增一行； false=点击新增整行即可新增一行数据
    tableOnlyAddIconTrigger: true,
    // 左右键能否切换当前聚焦的格子
    leftRightKeySwitch: false,
    // 时间输入框配置：true=按下enter即跳到下一格输入框
    timescapeEnterSwitch: false,
    // 手术表格 复制当前行时 需要复制的key：['OperGroupNo''OprnOprtBegntime','OprnConTime','Operator','Firstasst','Secondasst','AnaType','AnstLvCode','AnaDoc','OprnPatnType','OprnOprtEndtime','OperNote','AnstBegntime','AnstEndtime','OperDept','WoundHealingRateClass',]； 默认以上字段会复制，修改时请基于以上值追加/去除
    operationCopyKeys: [],
    // 诊断表格 复制当前行时 需要复制的key：基本同上
    icdeCopyKeys: [],
    // 是否需要备份：目前还没开发完,不要修改
    needDraft: false,
    // 诊断手术表格 是否开启批量选择删除
    icdeOperRowSelection: false,
    // 定制病案首页左侧搜索项文案：**根据需求让开发来配置，请不要随意修改
    dmrSearchParamLabels: {
      OutDate: '',
    },
    // 是否在首页搜索种显示“回收超过”选项
    dmrSearchRecycleShow: true,
    // PageUp PageDown是否可用于切换模：模块表示的是登记区域左侧导航部分
    dmrPageUpDownSwitchModule: false,
    // 左侧导航对应的输入格位置配置：true=当左侧模块切换，将对应的输入框滚动到 登记部分的最顶部
    menuViewTop: true,
    // 表格聚焦配置：true=表格中任意一输入格被聚焦时会将其滚动到 登记部分的顶部
    tableItemFocusTop: true,
    // 中心聚焦：当输入框聚焦时是否将其滚动到 登记部分 中心位置(**已弃用，请在生产环境严格设定为false)
    centerInputActivate: false,
    // 触发滚动到中心的距离：默认值50，当某一格距离底部少于50像素时自动将其滚动到登记部分中心（不建议随意改动）
    forceScrollToCenterDistance: 50,
    // 手术/诊断 是否强制第一条数据为主手术/主诊：手术有无主手术的可能，所以若表格内没有主手术，则依然不会强制第一个数据为主手术
    icdeOperFirstMain: false,
    // 表格自动追加一行未填行：生产环境建议设定为true，用于TAB enter等跳转的流畅性
    tableAutoAddNewLine: true,
    // 诊断复制后自动聚焦的key：当复制完诊断后 光标默认聚焦到复制的那一行的第一个可填写的格子，设定此Flag后会聚焦到Flag指定的那一格（例：IcdeCode）
    icdeCopyFocusKey: '',
    // 手术复制后自动聚焦的key：当复制完手术后 光标默认聚焦到复制的那一行的第一个可填写的格子，设定此Flag后会聚焦到Flag指定的那一格（例：OperCode）
    operCopyFocusKey: '',
    // 只读字段单击选中：不建议设为true，当且仅当有具体需求再设定为true
    readonlyInputClickSelectAll: false,
    // 点击编辑按钮时滚动到的位置：false=点击编辑按钮会自动聚焦表单内第一个可编辑的格子同时滚动页面到那里；true=会根据目前用户页面看的表单位置来计算第一个可编辑的格子并聚焦
    inEditRemainScrollPosition: false,
    // 手术表格内 删除行时是否需要二次确认
    operDeleteConfirm: false,
    // 诊断表格内 删除行时是否需要二次确认
    icdeDeleteConfirm: false,
    // 是否要调用第三方质控 Api/Dmr/DmrCardBundle/ThirdPartyCheck
    thirdPartyCheck: false,
    // 首页表格 若在PublicMode下要默认隐藏的列：PublicMode是登陆进来时 通过Api/Account/User/GetUserInfo 获取的Preferences内的PublicMode来判断的
    publicModeHideKeys: [],
    // 首页整个表单是否只读
    dmrRegistrationReadOnly: false,
    // 是否要强制在保存后自动开启右侧质控界面
    forceDmrRightReview: false,
    // 是否当质控审核无错误时自动跳转下一份
    saveAuditNoErrorAutoNext: false,
    // 是否双击唤起下拉框
    doubleClickPopUp: false,
    // 双击下拉框的exclude keys：当双击唤起下拉框时（doubleClickPopUp = true） 这里配置的 Module Key 强制不会触发双击唤起
    doubleClickPopUpFalseKeys: [],
    // 表格下拉高度
    tableSelectorDropdownHeight: 300,
    // 必填字段的样式，注释掉则显示 星号
    requiredLabelHighlight: {
      color: '#eb5757',
      fontWeight: 'bold',
    },
    // 表格整体高亮样式，注释掉则 不显示高亮
    tableHighlight: {
      outline: '2px solid #eb5757',
      borderRadius: 4,
    },
    // 退格键 是否可以一次删除格子内所有内容
    enableBackspaceDelete: false,
    // 表格内 手术诊断下拉框：true=展示的内容类似一个表格，false=展示的内容跟其他下拉框一样
    enableTableDropdownNG: true,
    // 诊断手术等表格内下拉框 1~9 数字键可以快捷选择 对应编码：enableTableDropdownNG=true 时才生效
    enableKeyboardFriendlySelect: false,
    // 诊断手术等表格内下拉框 自动选中第一条：enableTableDropdownNG=true 时才生效（选中≠填入表格）
    keyboardFriendlyDefaultSelectFirst: true,
    // 诊断手术等表格内下拉框 按下enter时自动填入第一条：enableTableDropdownNG=true 时才生效
    keyboardFriendlyEnterSelectFirst: false,
    // 诊断手术等表格内下拉框 翻页按键 keycode：enableTableDropdownNG=true 时才生效，33=PageUp，34=PageDown
    keyboardFlipKeys: {
      prev: [33],
      next: [34],
    },
    // 首页质控界面打开时的位置：right / bottom 只能2选1
    preCheckContainerPosition: 'right',
    // 是否启用医生端双栏：不代表默认展示双栏
    enableDoubleDeckDoctorEmr: true,
    // 是否启用前置评审
    preCardExamineEnable: true,
    // 前置评审类型：comment / checkbox
    preCardExamineType: 'comment',
    // 是否启用手术组套输入：仅enableTableDropdownNG=false时生效，若enableTableDropdownNG=true时会默认启用手术组套输入
    operationComboInput: false,
    // 手术组套选择时 默认要拼在最终Submit/Check时一起附加上的数据：一般情况下不需要修改
    operationComboDefaultMap: {
      WoundHealingRateClass: '11',
    },
    // 手术组套的额外规则：一般情况下不需要修改
    extraOperationComboRules: ['HasComboItem', 'FirstItem'],
    // 要不要保存新生儿附页的数据
    babyWithExtra: true,
    // 是否需要底部跑马灯显示 上一份病历信息
    footerShowPrevious: false,
  },

  // 评审相关配置
  qualityExamine: {
    // 是否显示评审界面的 统计区域：默认true即可
    examineSummaryShow: true,
  },

  // 结算清单相关配置
  chs: {
    // 同dmr，但是因为结算清单目前没有表单配置Layout，所以用true强制使用前端本地的Layout
    updateLayout: true,
    // 同dmr的enableTableDropdownNG
    numberSelectItem: true,
    // 是否在表格中展示首页的诊断&手术
    srcIcdeOperShow: false,
    // 是否显示 第三方质控 按钮
    showThirdPartyCheckBtn: false,
  },

  // 组合查询相关配置
  statsAnalysis: {
    // 组合查询页面版本：现在默认都是3了，tab形式的版本
    version: 3,
    // 组合查询tag样式：不要修改
    tagBaseStyle: {
      fontSize: 13,
      padding: '2px 7px',
      // fontSize: 16,
      // padding: '4px 12px',
    },
    // 是否 显示数据作图表 按钮
    drawChart: true,
    // 那些级联选择的输入框，是否要过滤notSelectable的数据：true=不需要过滤；false=需要过滤。默认true即可
    cascaderDictionaryName: true,
    // 组合查询 输入括号的地方 能不能使用数字快捷输入
    bracketInputUseNumber: true,
    // 开启 当且仅当选择了时间即可查询
    expressionEmptySearch: false,
    // 组合查询页面内 打开的病案首页 点击保存时 要不要 自动关闭首页弹框
    fromCombineQueryOpenDmrAutoClose: false,
  },

  // 首页质控相关配置：主要针对 病案首页质量监管 内的页面
  qualityControl: {
    // 是否使用新版（tree-table形式）的 质控审核规则配置 界面：默认为true即可
    useNewRuleSetting: true,

    // 编码能效页面配置
    codeValueQuantification: {
      // 是否开启日期趋势：默认true即可
      isOpenDateTrend: true,
      // 是否需要过滤 下方drgsName的表格列
      excludeDrgs: true,
      // 要过滤的表格列的中文名 title。目前只支持string，仅1个
      drgsName: '绩效DRGs',
    },
  },

  // 运营数据管理页面配置
  operationalDataManagement: {
    //是否显示批量退回按钮：每日住院登记 & 科室住院登记 才有
    isShowBatchReturnBtn: false,
    //是否显示转归同步按钮：每日住院登记 & 科室住院登记 才有
    isShowSyncIcdeBtn: false,
    // 编辑情况下 编辑完成一行时 换行默认焦点 需要跳过的key：一般用于跳过初始化已经生成的字段
    skipFocusKeys: ['ApprovedBedsNumber', 'SuppliedBedsNumber', 'StayInCnt'],
    // 是否使用v2接口：住院动态床位数管理 & 住院动态校对 才有
    useV2Page: true,
  },

  // 维护页面配置
  reportSys: {
    // 自定义报表配置界面 默认存在的appCodes：不要修改即可
    extraAppCodes: ['PublicReport', 'HospReportHighlight'],
  },

  // 医保 DRG/DIP 页面配置
  his: {
    // 要不要使用v2接口
    useV2: true,
    // 统计区域 要不要展示组数 & 入组率
    statsShowGrpCnt: true,
    // 费用分析 用于chart的统计 需要隐藏的数据
    feeCharge: {
      // 统计类别字典：StatsChargeType
      statsItemsHide: [],
      // 项目类别字典：MedChargeType：02=挂号费，13=诊察费
      medItemsHide: ['02', '13'],
    },

    // drg 支付明细查询 专用
    drgPayDetail: {
      // 是否显示病例图标icon
      showStatus: false,
    },

    // 测试页面时 方便用的一些操作/按钮：刷新表格/图标数据；标记列配置等等。别修改即可
    devtool: {
      refresh: true,
      editColumn: true,
    },
  },

  // 前置页面配置
  external: {
    // 默认false 一般不用修改
    emrWithHisId: false,

    // 前置页面 右侧容器 要隐藏的模块：可选参数有 医保预分组，质控审核 （目前已知：上海儿童 需要隐藏医保预分组）；注意参数是中文
    rightContainerHide: [],
  },
};
